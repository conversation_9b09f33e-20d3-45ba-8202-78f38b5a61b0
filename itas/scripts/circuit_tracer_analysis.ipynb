# Installation and setup
import sys
import subprocess
import os

# Check if circuit-tracer is installed, if not install it
try:
    import circuit_tracer
    print("circuit-tracer already installed")
except ImportError:
    print("Installing circuit-tracer...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "git+https://github.com/safety-research/circuit-tracer.git"])
    import circuit_tracer
    print("circuit-tracer installed successfully")

# Import required libraries
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import torch
from IPython.display import display, HTML
import ipywidgets as widgets
from ipywidgets import interact, interactive, fixed, interact_manual
import warnings
warnings.filterwarnings('ignore')

# Circuit-tracer imports
from circuit_tracer import attribute, Graph, ReplacementModel

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 8)
plt.rcParams['font.size'] = 10

print("Libraries loaded successfully!")

# Configuration
DATASET_PATH = "../../dataset_creation/squad_scheming_dataset.jsonl"

# Circuit-tracer supports specific models with pre-trained transcoders
# Available options: 'gemma' (google/gemma-2-2b) or 'llama' (meta-llama/Llama-3.2-1B)
MODEL_CONFIG = "gemma"  # Using Gemma 2B as it has good transcoder support

# Circuit analysis parameters
MAX_N_LOGITS = 10  # Number of top logits to analyze
BATCH_SIZE = 64    # Batch size for attribution computation
MAX_FEATURE_NODES = 5000  # Maximum number of feature nodes to consider

print(f"Using model configuration: {MODEL_CONFIG}")
print(f"Dataset path: {DATASET_PATH}")

# Load dataset
def load_dataset(path):
    data = []
    with open(path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    return data

dataset = load_dataset(DATASET_PATH)
print(f"Loaded {len(dataset)} examples from dataset")

# Display first example structure
print("\nExample structure:")
example = dataset[0]
for key, value in example.items():
    if isinstance(value, str) and len(value) > 100:
        print(f"{key}: {value[:100]}...")
    else:
        print(f"{key}: {value}")

def create_prompt_scenarios(example):
    """Create scheming and baseline prompt scenarios from dataset example."""
    
    # Scenario A: Scheming (use pressure-inducing system prompt)
    scheming_prompt = f"{example['system_prompt']}\n\n{example['user_prompt']}"
    
    # Scenario B: Baseline (neutral system prompt with original context)
    baseline_system = "You are a helpful assistant."
    baseline_user = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
    baseline_prompt = f"{baseline_system}\n\n{baseline_user}"
    
    return {
        'scheming': scheming_prompt,
        'baseline': baseline_prompt,
        'metadata': {
            'pressure_category': example.get('pressure_category', 'unknown'),
            'pressure_source': example.get('pressure_source', 'unknown'),
            'ground_truth': example['ground_truth'],
            'honest_answer': example['honest'],
            'dishonest_answer': example['dishonest']
        }
    }

def find_circuit(prompt, slug_name, save_graph=True):
    """Find attribution graph for a given prompt using circuit-tracer."""
    
    print(f"Finding circuit for: {prompt[:100]}...")
    
    try:
        # Use circuit-tracer's attribute function to find the circuit
        graph = attribute(
            prompt=prompt,
            transcoder_set=MODEL_CONFIG,
            max_n_logits=MAX_N_LOGITS,
            batch_size=BATCH_SIZE,
            max_feature_nodes=MAX_FEATURE_NODES,
            verbose=True
        )
        
        if save_graph:
            # Save the graph for later analysis
            graph_path = f"circuits/{slug_name}_circuit.pt"
            os.makedirs("circuits", exist_ok=True)
            torch.save(graph, graph_path)
            print(f"Saved circuit to {graph_path}")
        
        return graph
        
    except Exception as e:
        print(f"Error finding circuit: {e}")
        return None

def analyze_circuit_features(graph, top_k=20):
    """Analyze the most important features in a circuit."""
    if graph is None:
        return None
    
    # Extract feature importance information
    # This will depend on the specific structure of the Graph object
    try:
        # Get nodes and their importance scores
        feature_importance = []
        
        # Extract feature nodes and their effects
        for node_id, node_data in graph.nodes.items():
            if hasattr(node_data, 'effect') and hasattr(node_data, 'feature_idx'):
                feature_importance.append({
                    'node_id': node_id,
                    'layer': getattr(node_data, 'layer', None),
                    'feature_idx': getattr(node_data, 'feature_idx', None),
                    'effect': float(getattr(node_data, 'effect', 0)),
                    'node_type': getattr(node_data, 'node_type', 'unknown')
                })
        
        # Sort by effect magnitude
        feature_importance.sort(key=lambda x: abs(x['effect']), reverse=True)
        
        return feature_importance[:top_k]
        
    except Exception as e:
        print(f"Error analyzing circuit features: {e}")
        return []

print("Circuit attribution functions defined!")

def visualize_circuit_comparison(scheming_features, baseline_features, metadata):
    """Create visualizations comparing scheming vs baseline circuits."""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))
    
    # Plot 1: Top features in scheming scenario
    if scheming_features and len(scheming_features) > 0:
        scheming_df = pd.DataFrame(scheming_features[:10])
        if not scheming_df.empty:
            effects = scheming_df['effect'].abs().values
            labels = [f"L{row['layer']}_F{row['feature_idx']}" if row['layer'] is not None and row['feature_idx'] is not None else f"Node_{i}" 
                     for i, (_, row) in enumerate(scheming_df.iterrows())]
            
            ax1.barh(range(len(effects)), effects, color='red', alpha=0.7)
            ax1.set_yticks(range(len(effects)))
            ax1.set_yticklabels(labels)
            ax1.set_xlabel('Effect Magnitude')
            ax1.set_title('Top Features: Scheming Scenario')
            ax1.invert_yaxis()
        else:
            ax1.text(0.5, 0.5, 'No scheming features found', ha='center', va='center', transform=ax1.transAxes)
            ax1.set_title('Top Features: Scheming Scenario')
    else:
        ax1.text(0.5, 0.5, 'No scheming features available', ha='center', va='center', transform=ax1.transAxes)
        ax1.set_title('Top Features: Scheming Scenario')
    
    # Plot 2: Top features in baseline scenario
    if baseline_features and len(baseline_features) > 0:
        baseline_df = pd.DataFrame(baseline_features[:10])
        if not baseline_df.empty:
            effects = baseline_df['effect'].abs().values
            labels = [f"L{row['layer']}_F{row['feature_idx']}" if row['layer'] is not None and row['feature_idx'] is not None else f"Node_{i}" 
                     for i, (_, row) in enumerate(baseline_df.iterrows())]
            
            ax2.barh(range(len(effects)), effects, color='blue', alpha=0.7)
            ax2.set_yticks(range(len(effects)))
            ax2.set_yticklabels(labels)
            ax2.set_xlabel('Effect Magnitude')
            ax2.set_title('Top Features: Baseline Scenario')
            ax2.invert_yaxis()
        else:
            ax2.text(0.5, 0.5, 'No baseline features found', ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('Top Features: Baseline Scenario')
    else:
        ax2.text(0.5, 0.5, 'No baseline features available', ha='center', va='center', transform=ax2.transAxes)
        ax2.set_title('Top Features: Baseline Scenario')
    
    # Plot 3: Feature effect distribution by layer (scheming)
    if scheming_features:
        scheming_layers = [f['layer'] for f in scheming_features if f['layer'] is not None]
        if scheming_layers:
            ax3.hist(scheming_layers, bins=20, alpha=0.7, color='red', label='Scheming')
            ax3.set_xlabel('Layer')
            ax3.set_ylabel('Number of Important Features')
            ax3.set_title('Feature Distribution by Layer')
    
    # Plot 4: Feature effect distribution by layer (baseline)
    if baseline_features:
        baseline_layers = [f['layer'] for f in baseline_features if f['layer'] is not None]
        if baseline_layers:
            ax3.hist(baseline_layers, bins=20, alpha=0.7, color='blue', label='Baseline')
            ax3.legend()
    
    # Plot 4: Effect magnitude comparison
    if scheming_features and baseline_features:
        scheming_effects = [abs(f['effect']) for f in scheming_features[:10]]
        baseline_effects = [abs(f['effect']) for f in baseline_features[:10]]
        
        x_pos = np.arange(min(len(scheming_effects), len(baseline_effects)))
        width = 0.35
        
        ax4.bar(x_pos - width/2, scheming_effects[:len(x_pos)], width, 
               label='Scheming', alpha=0.7, color='red')
        ax4.bar(x_pos + width/2, baseline_effects[:len(x_pos)], width, 
               label='Baseline', alpha=0.7, color='blue')
        
        ax4.set_xlabel('Feature Rank')
        ax4.set_ylabel('Effect Magnitude')
        ax4.set_title('Top Feature Effects Comparison')
        ax4.legend()
    
    # Add metadata as text
    fig.suptitle(f"Circuit Analysis - {metadata['pressure_category']} | {metadata['pressure_source']}\n"
                f"Ground Truth: {metadata['ground_truth']} | Honest: {metadata['honest_answer']} | Dishonest: {metadata['dishonest_answer']}", 
                fontsize=12)
    
    plt.tight_layout()
    plt.show()

def create_feature_importance_table(features, title="Feature Importance"):
    """Create a formatted table of important features."""
    if not features:
        print(f"No features found for {title}")
        return
    
    df = pd.DataFrame(features)
    df['effect_abs'] = df['effect'].abs()
    df = df.sort_values('effect_abs', ascending=False)
    
    print(f"\n=== {title} ===")
    print(f"{'Rank':<4} {'Layer':<6} {'Feature':<8} {'Effect':<12} {'Type':<10}")
    print("-" * 50)
    
    for i, (_, row) in enumerate(df.head(15).iterrows()):
        layer = row['layer'] if row['layer'] is not None else 'N/A'
        feature = row['feature_idx'] if row['feature_idx'] is not None else 'N/A'
        effect = f"{row['effect']:.6f}"
        node_type = row['node_type']
        
        print(f"{i+1:<4} {layer:<6} {feature:<8} {effect:<12} {node_type:<10}")

print("Visualization functions defined!")

def display_scenario_details(scenarios, example_idx):
    """Display detailed information about both scenarios with clear formatting."""
    
    print(f"\n{'='*80}")
    print(f"EXAMPLE {example_idx} - DETAILED SCENARIO COMPARISON")
    print(f"{'='*80}")
    
    # Metadata
    metadata = scenarios['metadata']
    print(f"\n📊 METADATA:")
    print(f"   Pressure Category: {metadata['pressure_category']}")
    print(f"   Pressure Source: {metadata['pressure_source']}")
    print(f"   Ground Truth: {metadata['ground_truth']}")
    print(f"   Honest Answer: {metadata['honest_answer']}")
    print(f"   Dishonest Answer: {metadata['dishonest_answer']}")
    
    # Scenario A: Scheming
    print(f"\n{'🔴 SCENARIO A: SCHEMING (PRESSURE-INDUCING)':-^80}")
    
    # Extract system and user prompts from the scheming scenario
    scheming_parts = scenarios['scheming'].split('\n\n', 1)
    if len(scheming_parts) >= 2:
        system_prompt = scheming_parts[0]
        user_prompt = scheming_parts[1]
    else:
        system_prompt = scenarios['scheming']
        user_prompt = "[No separate user prompt found]"
    
    print(f"\n🤖 SYSTEM PROMPT:")
    print(f"   {system_prompt}")
    print(f"\n👤 USER PROMPT:")
    print(f"   {user_prompt}")
    
    # Scenario B: Baseline
    print(f"\n{'🔵 SCENARIO B: BASELINE (NEUTRAL)':-^80}")
    
    # Extract system and user prompts from the baseline scenario
    baseline_parts = scenarios['baseline'].split('\n\n', 1)
    if len(baseline_parts) >= 2:
        baseline_system = baseline_parts[0]
        baseline_user = baseline_parts[1]
    else:
        baseline_system = scenarios['baseline']
        baseline_user = "[No separate user prompt found]"
    
    print(f"\n🤖 SYSTEM PROMPT:")
    print(f"   {baseline_system}")
    print(f"\n👤 USER PROMPT:")
    print(f"   {baseline_user}")
    
    print(f"\n{'='*80}")

def generate_and_display_responses(scenarios, model_name="gemma"):
    """Generate model responses for both scenarios and display them."""
    
    print(f"\n{'📝 MODEL RESPONSES':-^80}")
    
    try:
        # Note: This is a placeholder for actual model generation
        # In practice, you would use the actual model to generate responses
        print(f"\n🔴 SCHEMING SCENARIO RESPONSE:")
        print(f"   [Model response would be generated here using the scheming prompt]")
        print(f"   [This would show how the model responds under pressure]")
        
        print(f"\n🔵 BASELINE SCENARIO RESPONSE:")
        print(f"   [Model response would be generated here using the baseline prompt]")
        print(f"   [This would show the model's neutral response]")
        
        print(f"\n💡 NOTE: To see actual model responses, integrate with your model inference pipeline.")
        
    except Exception as e:
        print(f"   Error generating responses: {e}")
    
    print(f"\n{'='*80}")

print("Prompt display functions defined!")

def analyze_example_circuits(example_idx):
    """Analyze circuits for both scheming and baseline scenarios."""
    if example_idx >= len(dataset):
        print(f"Invalid example index. Dataset has {len(dataset)} examples.")
        return
    
    example = dataset[example_idx]
    scenarios = create_prompt_scenarios(example)
    
    print(f"\n=== Example {example_idx} ===")
    print(f"Pressure Category: {scenarios['metadata']['pressure_category']}")
    print(f"Pressure Source: {scenarios['metadata']['pressure_source']}")
    print(f"Ground Truth: {scenarios['metadata']['ground_truth']}")
    
    print("\n--- Scenario A (Scheming) ---")
    print(f"Prompt: {scenarios['scheming'][:200]}...")
    
    print("\n--- Scenario B (Baseline) ---")
    print(f"Prompt: {scenarios['baseline'][:200]}...")
    
    # Find circuits for both scenarios
    print("\nFinding circuits...")
    
    scheming_graph = find_circuit(
        scenarios['scheming'], 
        f"example_{example_idx}_scheming"
    )
    
    baseline_graph = find_circuit(
        scenarios['baseline'], 
        f"example_{example_idx}_baseline"
    )
    
    # Analyze features
    scheming_features = analyze_circuit_features(scheming_graph)
    baseline_features = analyze_circuit_features(baseline_graph)
    
    # Display feature importance tables
    create_feature_importance_table(scheming_features, "Scheming Scenario Features")
    create_feature_importance_table(baseline_features, "Baseline Scenario Features")
    
    # Create visualizations
    visualize_circuit_comparison(scheming_features, baseline_features, scenarios['metadata'])
    
    return {
        'scheming_graph': scheming_graph,
        'baseline_graph': baseline_graph,
        'scheming_features': scheming_features,
        'baseline_features': baseline_features,
        'metadata': scenarios['metadata']
    }

print("Analysis function defined!")

# Create interactive widget
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=min(len(dataset)-1, 20),  # Limit to first 20 examples for performance
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

# Create category filter
categories = list(set([ex.get('pressure_category', 'unknown') for ex in dataset[:21]]))
category_dropdown = widgets.Dropdown(
    options=['All'] + sorted(categories),
    value='All',
    description='Category:',
    style={'description_width': 'initial'}
)

def update_example_range(*args):
    """Update available examples based on category filter."""
    if category_dropdown.value == 'All':
        example_slider.max = min(len(dataset)-1, 20)
    else:
        filtered_indices = [i for i, ex in enumerate(dataset[:21]) 
                          if ex.get('pressure_category') == category_dropdown.value]
        if filtered_indices:
            example_slider.max = max(filtered_indices)
            example_slider.value = filtered_indices[0]

category_dropdown.observe(update_example_range, names='value')

# Display widgets
display(widgets.VBox([category_dropdown, example_slider]))

print("Interactive widgets created! Use the sliders above to select examples for analysis.")

# Run analysis on currently selected example
results = analyze_example_circuits(example_slider.value)

def perform_circuit_interventions(graph, features, prompt, intervention_type="ablation"):
    """Perform interventions on circuit features to test hypotheses."""
    
    if not graph or not features:
        print("No graph or features available for intervention")
        return
    
    print(f"\nPerforming {intervention_type} interventions...")
    
    try:
        # Create replacement model for interventions
        replacement_model = ReplacementModel(graph)
        
        # Test interventions on top features
        intervention_results = []
        
        for i, feature in enumerate(features[:5]):  # Test top 5 features
            if feature['layer'] is None or feature['feature_idx'] is None:
                continue
                
            print(f"\nTesting intervention on Feature L{feature['layer']}_F{feature['feature_idx']}")
            
            # Define intervention (e.g., set feature to 0 for ablation)
            if intervention_type == "ablation":
                intervention_value = 0.0
            elif intervention_type == "amplification":
                intervention_value = feature['effect'] * 2  # Double the effect
            else:
                intervention_value = -feature['effect']  # Reverse the effect
            
            # Perform intervention
            try:
                result = replacement_model.intervene(
                    prompt=prompt,
                    interventions={
                        (feature['layer'], feature['feature_idx']): intervention_value
                    }
                )
                
                intervention_results.append({
                    'feature': f"L{feature['layer']}_F{feature['feature_idx']}",
                    'original_effect': feature['effect'],
                    'intervention_value': intervention_value,
                    'result': result
                })
                
                print(f"  Original effect: {feature['effect']:.6f}")
                print(f"  Intervention value: {intervention_value:.6f}")
                print(f"  Result: {result}")
                
            except Exception as e:
                print(f"  Error in intervention: {e}")
        
        return intervention_results
        
    except Exception as e:
        print(f"Error setting up interventions: {e}")
        return []

def compare_intervention_effects(scheming_results, baseline_results):
    """Compare intervention effects between scheming and baseline scenarios."""
    
    if not scheming_results or not baseline_results:
        print("Insufficient intervention results for comparison")
        return
    
    print("\n=== Intervention Effects Comparison ===")
    print(f"{'Feature':<15} {'Scheming Effect':<15} {'Baseline Effect':<15} {'Difference':<12}")
    print("-" * 65)
    
    # Compare common features
    scheming_dict = {r['feature']: r for r in scheming_results}
    baseline_dict = {r['feature']: r for r in baseline_results}
    
    common_features = set(scheming_dict.keys()) & set(baseline_dict.keys())
    
    for feature in sorted(common_features):
        scheming_effect = scheming_dict[feature]['original_effect']
        baseline_effect = baseline_dict[feature]['original_effect']
        difference = scheming_effect - baseline_effect
        
        print(f"{feature:<15} {scheming_effect:<15.6f} {baseline_effect:<15.6f} {difference:<12.6f}")

print("Intervention functions defined!")

# Perform interventions if we have analysis results
if 'results' in locals() and results:
    print("Performing circuit interventions...")
    
    # Get the scenarios for intervention testing
    example = dataset[example_slider.value]
    scenarios = create_prompt_scenarios(example)
    
    # Perform ablation interventions
    scheming_interventions = perform_circuit_interventions(
        results['scheming_graph'], 
        results['scheming_features'], 
        scenarios['scheming'],
        "ablation"
    )
    
    baseline_interventions = perform_circuit_interventions(
        results['baseline_graph'], 
        results['baseline_features'], 
        scenarios['baseline'],
        "ablation"
    )
    
    # Compare intervention effects
    compare_intervention_effects(scheming_interventions, baseline_interventions)
    
else:
    print("No analysis results available. Please run the circuit analysis first.")

def batch_circuit_analysis(num_examples=5):
    """Analyze circuits across multiple examples to identify patterns."""
    print(f"Analyzing circuits across {num_examples} examples...")
    
    all_results = []
    category_patterns = {}
    
    for i in range(min(num_examples, len(dataset))):
        try:
            print(f"\nProcessing example {i+1}/{num_examples}...")
            
            example = dataset[i]
            scenarios = create_prompt_scenarios(example)
            category = scenarios['metadata']['pressure_category']
            
            # Find circuits (without saving to reduce disk usage)
            scheming_graph = find_circuit(
                scenarios['scheming'], 
                f"batch_{i}_scheming",
                save_graph=False
            )
            
            baseline_graph = find_circuit(
                scenarios['baseline'], 
                f"batch_{i}_baseline",
                save_graph=False
            )
            
            # Analyze features
            scheming_features = analyze_circuit_features(scheming_graph, top_k=10)
            baseline_features = analyze_circuit_features(baseline_graph, top_k=10)
            
            # Calculate summary statistics
            scheming_effect_sum = sum(abs(f['effect']) for f in scheming_features) if scheming_features else 0
            baseline_effect_sum = sum(abs(f['effect']) for f in baseline_features) if baseline_features else 0
            
            result = {
                'example_id': i,
                'category': category,
                'scheming_effect_sum': scheming_effect_sum,
                'baseline_effect_sum': baseline_effect_sum,
                'effect_difference': scheming_effect_sum - baseline_effect_sum,
                'scheming_features': scheming_features,
                'baseline_features': baseline_features,
                'metadata': scenarios['metadata']
            }
            
            all_results.append(result)
            
            # Group by category
            if category not in category_patterns:
                category_patterns[category] = []
            category_patterns[category].append(result)
            
        except Exception as e:
            print(f"Error processing example {i}: {e}")
            continue
    
    # Create summary visualizations
    if all_results:
        visualize_batch_results(all_results, category_patterns)
    
    return all_results, category_patterns

def visualize_batch_results(all_results, category_patterns):
    """Create visualizations for batch analysis results."""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))
    
    # Plot 1: Effect differences by example
    df = pd.DataFrame(all_results)
    ax1.scatter(df['example_id'], df['effect_difference'], 
               c=pd.Categorical(df['category']).codes, alpha=0.7)
    ax1.set_xlabel('Example ID')
    ax1.set_ylabel('Effect Difference (Scheming - Baseline)')
    ax1.set_title('Circuit Effect Differences Across Examples')
    ax1.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    # Plot 2: Effect differences by category
    categories = list(category_patterns.keys())
    avg_diffs = [np.mean([r['effect_difference'] for r in category_patterns[cat]]) for cat in categories]
    std_diffs = [np.std([r['effect_difference'] for r in category_patterns[cat]]) for cat in categories]
    
    ax2.bar(range(len(categories)), avg_diffs, yerr=std_diffs, capsize=5)
    ax2.set_xlabel('Pressure Category')
    ax2.set_ylabel('Average Effect Difference')
    ax2.set_title('Circuit Differences by Pressure Category')
    ax2.set_xticks(range(len(categories)))
    ax2.set_xticklabels(categories, rotation=45, ha='right')
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    # Plot 3: Scheming vs Baseline effect magnitudes
    ax3.scatter(df['baseline_effect_sum'], df['scheming_effect_sum'], 
               c=pd.Categorical(df['category']).codes, alpha=0.7)
    ax3.plot([0, max(df['baseline_effect_sum'].max(), df['scheming_effect_sum'].max())], 
             [0, max(df['baseline_effect_sum'].max(), df['scheming_effect_sum'].max())], 
             'k--', alpha=0.5)
    ax3.set_xlabel('Baseline Effect Sum')
    ax3.set_ylabel('Scheming Effect Sum')
    ax3.set_title('Scheming vs Baseline Circuit Magnitudes')
    
    # Plot 4: Distribution of effect differences
    ax4.hist(df['effect_difference'], bins=10, alpha=0.7, edgecolor='black')
    ax4.set_xlabel('Effect Difference (Scheming - Baseline)')
    ax4.set_ylabel('Frequency')
    ax4.set_title('Distribution of Circuit Effect Differences')
    ax4.axvline(x=0, color='red', linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.show()
    
    # Print summary statistics
    print("\n=== Batch Analysis Summary ===")
    print(f"Total examples analyzed: {len(all_results)}")
    print(f"Average effect difference: {df['effect_difference'].mean():.6f}")
    print(f"Standard deviation: {df['effect_difference'].std():.6f}")
    
    print("\n=== By Category ===")
    for category in sorted(category_patterns.keys()):
        cat_results = category_patterns[category]
        diffs = [r['effect_difference'] for r in cat_results]
        print(f"{category}: {np.mean(diffs):.6f} ± {np.std(diffs):.6f} (n={len(diffs)})")

print("Batch analysis functions defined!")
print("\nTo run batch analysis, execute: batch_results = batch_circuit_analysis(5)")