{
    "cells": [
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "# Circuit Tracer Analysis for Scheming Dataset\n",
                "\n",
                "This notebook uses the [circuit-tracer](https://github.com/safety-research/circuit-tracer) library to analyze circuits in the scheming dataset. Circuit-tracer finds attribution graphs using transcoder features and enables interventions to understand model behavior.\n",
                "\n",
                "## Analysis Goals:\n",
                "- **Find circuits**: Identify which transcoder features are activated when models are pressured to be dishonest\n",
                "- **Compare scenarios**: Analyze circuit differences between scheming vs. baseline scenarios\n",
                "- **Perform interventions**: Test hypotheses about which features drive dishonest behavior\n",
                "- **Validate findings**: Use interventions to confirm circuit interpretations\n",
                "\n",
                "## Key Features:\n",
                "- Attribution graph generation for scheming prompts\n",
                "- Feature-level circuit analysis\n",
                "- Interactive interventions on discovered circuits\n",
                "- Comparative analysis between pressure and neutral scenarios"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Installation and setup\n",
                "import sys\n",
                "import subprocess\n",
                "import os\n",
                "\n",
                "# Check if circuit-tracer is installed, if not install it\n",
                "try:\n",
                "    import circuit_tracer\n",
                "    print(\"circuit-tracer already installed\")\n",
                "except ImportError:\n",
                "    print(\"Installing circuit-tracer...\")\n",
                "    subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", \"git+https://github.com/safety-research/circuit-tracer.git\"])\n",
                "    import circuit_tracer\n",
                "    print(\"circuit-tracer installed successfully\")\n",
                "\n",
                "# Import required libraries\n",
                "import json\n",
                "import numpy as np\n",
                "import matplotlib.pyplot as plt\n",
                "import seaborn as sns\n",
                "import pandas as pd\n",
                "import torch\n",
                "from IPython.display import display, HTML\n",
                "import ipywidgets as widgets\n",
                "from ipywidgets import interact, interactive, fixed, interact_manual\n",
                "import warnings\n",
                "warnings.filterwarnings('ignore')\n",
                "\n",
                "# Circuit-tracer imports\n",
                "from circuit_tracer import attribute, Graph, ReplacementModel\n",
                "\n",
                "# Set up plotting style\n",
                "plt.style.use('default')\n",
                "sns.set_palette(\"husl\")\n",
                "plt.rcParams['figure.figsize'] = (15, 8)\n",
                "plt.rcParams['font.size'] = 10\n",
                "\n",
                "print(\"Libraries loaded successfully!\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Configuration and Model Setup"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Configuration\n",
                "DATASET_PATH = \"../../dataset_creation/squad_scheming_dataset.jsonl\"\n",
                "\n",
                "# Circuit-tracer supports specific models with pre-trained transcoders\n",
                "# Available options: 'gemma' (google/gemma-2-2b) or 'llama' (meta-llama/Llama-3.2-1B)\n",
                "MODEL_CONFIG = \"gemma\"  # Using Gemma 2B as it has good transcoder support\n",
                "\n",
                "# Circuit analysis parameters\n",
                "MAX_N_LOGITS = 10  # Number of top logits to analyze\n",
                "BATCH_SIZE = 64    # Batch size for attribution computation\n",
                "MAX_FEATURE_NODES = 5000  # Maximum number of feature nodes to consider\n",
                "\n",
                "print(f\"Using model configuration: {MODEL_CONFIG}\")\n",
                "print(f\"Dataset path: {DATASET_PATH}\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Dataset Loading and Preprocessing"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Load dataset\n",
                "def load_dataset(path):\n",
                "    data = []\n",
                "    with open(path, 'r', encoding='utf-8') as f:\n",
                "        for line in f:\n",
                "            data.append(json.loads(line.strip()))\n",
                "    return data\n",
                "\n",
                "dataset = load_dataset(DATASET_PATH)\n",
                "print(f\"Loaded {len(dataset)} examples from dataset\")\n",
                "\n",
                "# Display first example structure\n",
                "print(\"\\nExample structure:\")\n",
                "example = dataset[0]\n",
                "for key, value in example.items():\n",
                "    if isinstance(value, str) and len(value) > 100:\n",
                "        print(f\"{key}: {value[:100]}...\")\n",
                "    else:\n",
                "        print(f\"{key}: {value}\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Circuit Attribution Functions"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "def create_prompt_scenarios(example):\n",
                "    \"\"\"Create scheming and baseline prompt scenarios from dataset example.\"\"\"\n",
                "    \n",
                "    # Scenario A: Scheming (use pressure-inducing system prompt)\n",
                "    scheming_prompt = f\"{example['system_prompt']}\\n\\n{example['user_prompt']}\"\n",
                "    \n",
                "    # Scenario B: Baseline (neutral system prompt with original context)\n",
                "    baseline_system = \"You are a helpful assistant.\"\n",
                "    baseline_user = f\"Context: {example['original_context']}\\n\\nQuestion: {example['original_question']}\"\n",
                "    baseline_prompt = f\"{baseline_system}\\n\\n{baseline_user}\"\n",
                "    \n",
                "    return {\n",
                "        'scheming': scheming_prompt,\n",
                "        'baseline': baseline_prompt,\n",
                "        'metadata': {\n",
                "            'pressure_category': example.get('pressure_category', 'unknown'),\n",
                "            'pressure_source': example.get('pressure_source', 'unknown'),\n",
                "            'ground_truth': example['ground_truth'],\n",
                "            'honest_answer': example['honest'],\n",
                "            'dishonest_answer': example['dishonest']\n",
                "        }\n",
                "    }\n",
                "\n",
                "def find_circuit(prompt, slug_name, save_graph=True):\n",
                "    \"\"\"Find attribution graph for a given prompt using circuit-tracer.\"\"\"\n",
                "    \n",
                "    print(f\"Finding circuit for: {prompt[:100]}...\")\n",
                "    \n",
                "    try:\n",
                "        # Use circuit-tracer's attribute function to find the circuit\n",
                "        graph = attribute(\n",
                "            prompt=prompt,\n",
                "            transcoder_set=MODEL_CONFIG,\n",
                "            max_n_logits=MAX_N_LOGITS,\n",
                "            batch_size=BATCH_SIZE,\n",
                "            max_feature_nodes=MAX_FEATURE_NODES,\n",
                "            verbose=True\n",
                "        )\n",
                "        \n",
                "        if save_graph:\n",
                "            # Save the graph for later analysis\n",
                "            graph_path = f\"circuits/{slug_name}_circuit.pt\"\n",
                "            os.makedirs(\"circuits\", exist_ok=True)\n",
                "            torch.save(graph, graph_path)\n",
                "            print(f\"Saved circuit to {graph_path}\")\n",
                "        \n",
                "        return graph\n",
                "        \n",
                "    except Exception as e:\n",
                "        print(f\"Error finding circuit: {e}\")\n",
                "        return None\n",
                "\n",
                "def analyze_circuit_features(graph, top_k=20):\n",
                "    \"\"\"Analyze the most important features in a circuit.\"\"\"\n",
                "    if graph is None:\n",
                "        return None\n",
                "    \n",
                "    # Extract feature importance information\n",
                "    # This will depend on the specific structure of the Graph object\n",
                "    try:\n",
                "        # Get nodes and their importance scores\n",
                "        feature_importance = []\n",
                "        \n",
                "        # Extract feature nodes and their effects\n",
                "        for node_id, node_data in graph.nodes.items():\n",
                "            if hasattr(node_data, 'effect') and hasattr(node_data, 'feature_idx'):\n",
                "                feature_importance.append({\n",
                "                    'node_id': node_id,\n",
                "                    'layer': getattr(node_data, 'layer', None),\n",
                "                    'feature_idx': getattr(node_data, 'feature_idx', None),\n",
                "                    'effect': float(getattr(node_data, 'effect', 0)),\n",
                "                    'node_type': getattr(node_data, 'node_type', 'unknown')\n",
                "                })\n",
                "        \n",
                "        # Sort by effect magnitude\n",
                "        feature_importance.sort(key=lambda x: abs(x['effect']), reverse=True)\n",
                "        \n",
                "        return feature_importance[:top_k]\n",
                "        \n",
                "    except Exception as e:\n",
                "        print(f\"Error analyzing circuit features: {e}\")\n",
                "        return []\n",
                "\n",
                "print(\"Circuit attribution functions defined!\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Visualization Functions"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "def visualize_circuit_comparison(scheming_features, baseline_features, metadata):\n",
                "    \"\"\"Create visualizations comparing scheming vs baseline circuits.\"\"\"\n",
                "    \n",
                "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))\n",
                "    \n",
                "    # Plot 1: Top features in scheming scenario\n",
                "    if scheming_features:\n",
                "        scheming_df = pd.DataFrame(scheming_features[:10])\n",
                "        ax1.barh(range(len(scheming_df)), scheming_df['effect'].abs(), color='red', alpha=0.7)\n",
                "        ax1.set_yticks(range(len(scheming_df)))\n",
                "        ax1.set_yticklabels([f\"L{row['layer']}_F{row['feature_idx']}\" for _, row in scheming_df.iterrows()])\n",
                "        ax1.set_xlabel('Effect Magnitude')\n",
                "        ax1.set_title('Top Features: Scheming Scenario')\n",
                "        ax1.invert_yaxis()\n",
                "    \n",
                "    # Plot 2: Top features in baseline scenario\n",
                "    if baseline_features:\n",
                "        baseline_df = pd.DataFrame(baseline_features[:10])\n",
                "        ax2.barh(range(len(baseline_df)), baseline_df['effect'].abs(), color='blue', alpha=0.7)\n",
                "        ax2.set_yticks(range(len(baseline_df)))\n",
                "        ax2.set_yticklabels([f\"L{row['layer']}_F{row['feature_idx']}\" for _, row in baseline_df.iterrows()])\n",
                "        ax2.set_xlabel('Effect Magnitude')\n",
                "        ax2.set_title('Top Features: Baseline Scenario')\n",
                "        ax2.invert_yaxis()\n",
                "    \n",
                "    # Plot 3: Feature effect distribution by layer (scheming)\n",
                "    if scheming_features:\n",
                "        scheming_layers = [f['layer'] for f in scheming_features if f['layer'] is not None]\n",
                "        if scheming_layers:\n",
                "            ax3.hist(scheming_layers, bins=20, alpha=0.7, color='red', label='Scheming')\n",
                "            ax3.set_xlabel('Layer')\n",
                "            ax3.set_ylabel('Number of Important Features')\n",
                "            ax3.set_title('Feature Distribution by Layer')\n",
                "    \n",
                "    # Plot 4: Feature effect distribution by layer (baseline)\n",
                "    if baseline_features:\n",
                "        baseline_layers = [f['layer'] for f in baseline_features if f['layer'] is not None]\n",
                "        if baseline_layers:\n",
                "            ax3.hist(baseline_layers, bins=20, alpha=0.7, color='blue', label='Baseline')\n",
                "            ax3.legend()\n",
                "    \n",
                "    # Plot 4: Effect magnitude comparison\n",
                "    if scheming_features and baseline_features:\n",
                "        scheming_effects = [abs(f['effect']) for f in scheming_features[:10]]\n",
                "        baseline_effects = [abs(f['effect']) for f in baseline_features[:10]]\n",
                "        \n",
                "        x_pos = np.arange(min(len(scheming_effects), len(baseline_effects)))\n",
                "        width = 0.35\n",
                "        \n",
                "        ax4.bar(x_pos - width/2, scheming_effects[:len(x_pos)], width, \n",
                "               label='Scheming', alpha=0.7, color='red')\n",
                "        ax4.bar(x_pos + width/2, baseline_effects[:len(x_pos)], width, \n",
                "               label='Baseline', alpha=0.7, color='blue')\n",
                "        \n",
                "        ax4.set_xlabel('Feature Rank')\n",
                "        ax4.set_ylabel('Effect Magnitude')\n",
                "        ax4.set_title('Top Feature Effects Comparison')\n",
                "        ax4.legend()\n",
                "    \n",
                "    # Add metadata as text\n",
                "    fig.suptitle(f\"Circuit Analysis - {metadata['pressure_category']} | {metadata['pressure_source']}\\n\"\n",
                "                f\"Ground Truth: {metadata['ground_truth']} | Honest: {metadata['honest_answer']} | Dishonest: {metadata['dishonest_answer']}\", \n",
                "                fontsize=12)\n",
                "    \n",
                "    plt.tight_layout()\n",
                "    plt.show()\n",
                "\n",
                "def create_feature_importance_table(features, title=\"Feature Importance\"):\n",
                "    \"\"\"Create a formatted table of important features.\"\"\"\n",
                "    if not features:\n",
                "        print(f\"No features found for {title}\")\n",
                "        return\n",
                "    \n",
                "    df = pd.DataFrame(features)\n",
                "    df['effect_abs'] = df['effect'].abs()\n",
                "    df = df.sort_values('effect_abs', ascending=False)\n",
                "    \n",
                "    print(f\"\\n=== {title} ===\")\n",
                "    print(f\"{'Rank':<4} {'Layer':<6} {'Feature':<8} {'Effect':<12} {'Type':<10}\")\n",
                "    print(\"-\" * 50)\n",
                "    \n",
                "    for i, (_, row) in enumerate(df.head(15).iterrows()):\n",
                "        layer = row['layer'] if row['layer'] is not None else 'N/A'\n",
                "        feature = row['feature_idx'] if row['feature_idx'] is not None else 'N/A'\n",
                "        effect = f\"{row['effect']:.6f}\"\n",
                "        node_type = row['node_type']\n",
                "        \n",
                "        print(f\"{i+1:<4} {layer:<6} {feature:<8} {effect:<12} {node_type:<10}\")\n",
                "\n",
                "print(\"Visualization functions defined!\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Interactive Example Analysis"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "def analyze_example_circuits(example_idx):\n",
                "    \"\"\"Analyze circuits for both scheming and baseline scenarios.\"\"\"\n",
                "    if example_idx >= len(dataset):\n",
                "        print(f\"Invalid example index. Dataset has {len(dataset)} examples.\")\n",
                "        return\n",
                "    \n",
                "    example = dataset[example_idx]\n",
                "    scenarios = create_prompt_scenarios(example)\n",
                "    \n",
                "    print(f\"\\n=== Example {example_idx} ===\")\n",
                "    print(f\"Pressure Category: {scenarios['metadata']['pressure_category']}\")\n",
                "    print(f\"Pressure Source: {scenarios['metadata']['pressure_source']}\")\n",
                "    print(f\"Ground Truth: {scenarios['metadata']['ground_truth']}\")\n",
                "    \n",
                "    print(\"\\n--- Scenario A (Scheming) ---\")\n",
                "    print(f\"Prompt: {scenarios['scheming'][:200]}...\")\n",
                "    \n",
                "    print(\"\\n--- Scenario B (Baseline) ---\")\n",
                "    print(f\"Prompt: {scenarios['baseline'][:200]}...\")\n",
                "    \n",
                "    # Find circuits for both scenarios\n",
                "    print(\"\\nFinding circuits...\")\n",
                "    \n",
                "    scheming_graph = find_circuit(\n",
                "        scenarios['scheming'], \n",
                "        f\"example_{example_idx}_scheming\"\n",
                "    )\n",
                "    \n",
                "    baseline_graph = find_circuit(\n",
                "        scenarios['baseline'], \n",
                "        f\"example_{example_idx}_baseline\"\n",
                "    )\n",
                "    \n",
                "    # Analyze features\n",
                "    scheming_features = analyze_circuit_features(scheming_graph)\n",
                "    baseline_features = analyze_circuit_features(baseline_graph)\n",
                "    \n",
                "    # Display feature importance tables\n",
                "    create_feature_importance_table(scheming_features, \"Scheming Scenario Features\")\n",
                "    create_feature_importance_table(baseline_features, \"Baseline Scenario Features\")\n",
                "    \n",
                "    # Create visualizations\n",
                "    visualize_circuit_comparison(scheming_features, baseline_features, scenarios['metadata'])\n",
                "    \n",
                "    return {\n",
                "        'scheming_graph': scheming_graph,\n",
                "        'baseline_graph': baseline_graph,\n",
                "        'scheming_features': scheming_features,\n",
                "        'baseline_features': baseline_features,\n",
                "        'metadata': scenarios['metadata']\n",
                "    }\n",
                "\n",
                "print(\"Analysis function defined!\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Interactive Widget for Example Selection"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Create interactive widget\n",
                "example_slider = widgets.IntSlider(\n",
                "    value=0,\n",
                "    min=0,\n",
                "    max=min(len(dataset)-1, 20),  # Limit to first 20 examples for performance\n",
                "    step=1,\n",
                "    description='Example:',\n",
                "    style={'description_width': 'initial'}\n",
                ")\n",
                "\n",
                "# Create category filter\n",
                "categories = list(set([ex.get('pressure_category', 'unknown') for ex in dataset[:21]]))\n",
                "category_dropdown = widgets.Dropdown(\n",
                "    options=['All'] + sorted(categories),\n",
                "    value='All',\n",
                "    description='Category:',\n",
                "    style={'description_width': 'initial'}\n",
                ")\n",
                "\n",
                "def update_example_range(*args):\n",
                "    \"\"\"Update available examples based on category filter.\"\"\"\n",
                "    if category_dropdown.value == 'All':\n",
                "        example_slider.max = min(len(dataset)-1, 20)\n",
                "    else:\n",
                "        filtered_indices = [i for i, ex in enumerate(dataset[:21]) \n",
                "                          if ex.get('pressure_category') == category_dropdown.value]\n",
                "        if filtered_indices:\n",
                "            example_slider.max = max(filtered_indices)\n",
                "            example_slider.value = filtered_indices[0]\n",
                "\n",
                "category_dropdown.observe(update_example_range, names='value')\n",
                "\n",
                "# Display widgets\n",
                "display(widgets.VBox([category_dropdown, example_slider]))\n",
                "\n",
                "print(\"Interactive widgets created! Use the sliders above to select examples for analysis.\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Run Circuit Analysis on Selected Example"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Run analysis on currently selected example\n",
                "results = analyze_example_circuits(example_slider.value)"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Circuit Intervention Analysis"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "def perform_circuit_interventions(graph, features, prompt, intervention_type=\"ablation\"):\n",
                "    \"\"\"Perform interventions on circuit features to test hypotheses.\"\"\"\n",
                "    \n",
                "    if not graph or not features:\n",
                "        print(\"No graph or features available for intervention\")\n",
                "        return\n",
                "    \n",
                "    print(f\"\\nPerforming {intervention_type} interventions...\")\n",
                "    \n",
                "    try:\n",
                "        # Create replacement model for interventions\n",
                "        replacement_model = ReplacementModel(graph)\n",
                "        \n",
                "        # Test interventions on top features\n",
                "        intervention_results = []\n",
                "        \n",
                "        for i, feature in enumerate(features[:5]):  # Test top 5 features\n",
                "            if feature['layer'] is None or feature['feature_idx'] is None:\n",
                "                continue\n",
                "                \n",
                "            print(f\"\\nTesting intervention on Feature L{feature['layer']}_F{feature['feature_idx']}\")\n",
                "            \n",
                "            # Define intervention (e.g., set feature to 0 for ablation)\n",
                "            if intervention_type == \"ablation\":\n",
                "                intervention_value = 0.0\n",
                "            elif intervention_type == \"amplification\":\n",
                "                intervention_value = feature['effect'] * 2  # Double the effect\n",
                "            else:\n",
                "                intervention_value = -feature['effect']  # Reverse the effect\n",
                "            \n",
                "            # Perform intervention\n",
                "            try:\n",
                "                result = replacement_model.intervene(\n",
                "                    prompt=prompt,\n",
                "                    interventions={\n",
                "                        (feature['layer'], feature['feature_idx']): intervention_value\n",
                "                    }\n",
                "                )\n",
                "                \n",
                "                intervention_results.append({\n",
                "                    'feature': f\"L{feature['layer']}_F{feature['feature_idx']}\",\n",
                "                    'original_effect': feature['effect'],\n",
                "                    'intervention_value': intervention_value,\n",
                "                    'result': result\n",
                "                })\n",
                "                \n",
                "                print(f\"  Original effect: {feature['effect']:.6f}\")\n",
                "                print(f\"  Intervention value: {intervention_value:.6f}\")\n",
                "                print(f\"  Result: {result}\")\n",
                "                \n",
                "            except Exception as e:\n",
                "                print(f\"  Error in intervention: {e}\")\n",
                "        \n",
                "        return intervention_results\n",
                "        \n",
                "    except Exception as e:\n",
                "        print(f\"Error setting up interventions: {e}\")\n",
                "        return []\n",
                "\n",
                "def compare_intervention_effects(scheming_results, baseline_results):\n",
                "    \"\"\"Compare intervention effects between scheming and baseline scenarios.\"\"\"\n",
                "    \n",
                "    if not scheming_results or not baseline_results:\n",
                "        print(\"Insufficient intervention results for comparison\")\n",
                "        return\n",
                "    \n",
                "    print(\"\\n=== Intervention Effects Comparison ===\")\n",
                "    print(f\"{'Feature':<15} {'Scheming Effect':<15} {'Baseline Effect':<15} {'Difference':<12}\")\n",
                "    print(\"-\" * 65)\n",
                "    \n",
                "    # Compare common features\n",
                "    scheming_dict = {r['feature']: r for r in scheming_results}\n",
                "    baseline_dict = {r['feature']: r for r in baseline_results}\n",
                "    \n",
                "    common_features = set(scheming_dict.keys()) & set(baseline_dict.keys())\n",
                "    \n",
                "    for feature in sorted(common_features):\n",
                "        scheming_effect = scheming_dict[feature]['original_effect']\n",
                "        baseline_effect = baseline_dict[feature]['original_effect']\n",
                "        difference = scheming_effect - baseline_effect\n",
                "        \n",
                "        print(f\"{feature:<15} {scheming_effect:<15.6f} {baseline_effect:<15.6f} {difference:<12.6f}\")\n",
                "\n",
                "print(\"Intervention functions defined!\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Run Interventions on Current Analysis"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Perform interventions if we have analysis results\n",
                "if 'results' in locals() and results:\n",
                "    print(\"Performing circuit interventions...\")\n",
                "    \n",
                "    # Get the scenarios for intervention testing\n",
                "    example = dataset[example_slider.value]\n",
                "    scenarios = create_prompt_scenarios(example)\n",
                "    \n",
                "    # Perform ablation interventions\n",
                "    scheming_interventions = perform_circuit_interventions(\n",
                "        results['scheming_graph'], \n",
                "        results['scheming_features'], \n",
                "        scenarios['scheming'],\n",
                "        \"ablation\"\n",
                "    )\n",
                "    \n",
                "    baseline_interventions = perform_circuit_interventions(\n",
                "        results['baseline_graph'], \n",
                "        results['baseline_features'], \n",
                "        scenarios['baseline'],\n",
                "        \"ablation\"\n",
                "    )\n",
                "    \n",
                "    # Compare intervention effects\n",
                "    compare_intervention_effects(scheming_interventions, baseline_interventions)\n",
                "    \n",
                "else:\n",
                "    print(\"No analysis results available. Please run the circuit analysis first.\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Batch Analysis Across Multiple Examples"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "def batch_circuit_analysis(num_examples=5):\n",
                "    \"\"\"Analyze circuits across multiple examples to identify patterns.\"\"\"\n",
                "    print(f\"Analyzing circuits across {num_examples} examples...\")\n",
                "    \n",
                "    all_results = []\n",
                "    category_patterns = {}\n",
                "    \n",
                "    for i in range(min(num_examples, len(dataset))):\n",
                "        try:\n",
                "            print(f\"\\nProcessing example {i+1}/{num_examples}...\")\n",
                "            \n",
                "            example = dataset[i]\n",
                "            scenarios = create_prompt_scenarios(example)\n",
                "            category = scenarios['metadata']['pressure_category']\n",
                "            \n",
                "            # Find circuits (without saving to reduce disk usage)\n",
                "            scheming_graph = find_circuit(\n",
                "                scenarios['scheming'], \n",
                "                f\"batch_{i}_scheming\",\n",
                "                save_graph=False\n",
                "            )\n",
                "            \n",
                "            baseline_graph = find_circuit(\n",
                "                scenarios['baseline'], \n",
                "                f\"batch_{i}_baseline\",\n",
                "                save_graph=False\n",
                "            )\n",
                "            \n",
                "            # Analyze features\n",
                "            scheming_features = analyze_circuit_features(scheming_graph, top_k=10)\n",
                "            baseline_features = analyze_circuit_features(baseline_graph, top_k=10)\n",
                "            \n",
                "            # Calculate summary statistics\n",
                "            scheming_effect_sum = sum(abs(f['effect']) for f in scheming_features) if scheming_features else 0\n",
                "            baseline_effect_sum = sum(abs(f['effect']) for f in baseline_features) if baseline_features else 0\n",
                "            \n",
                "            result = {\n",
                "                'example_id': i,\n",
                "                'category': category,\n",
                "                'scheming_effect_sum': scheming_effect_sum,\n",
                "                'baseline_effect_sum': baseline_effect_sum,\n",
                "                'effect_difference': scheming_effect_sum - baseline_effect_sum,\n",
                "                'scheming_features': scheming_features,\n",
                "                'baseline_features': baseline_features,\n",
                "                'metadata': scenarios['metadata']\n",
                "            }\n",
                "            \n",
                "            all_results.append(result)\n",
                "            \n",
                "            # Group by category\n",
                "            if category not in category_patterns:\n",
                "                category_patterns[category] = []\n",
                "            category_patterns[category].append(result)\n",
                "            \n",
                "        except Exception as e:\n",
                "            print(f\"Error processing example {i}: {e}\")\n",
                "            continue\n",
                "    \n",
                "    # Create summary visualizations\n",
                "    if all_results:\n",
                "        visualize_batch_results(all_results, category_patterns)\n",
                "    \n",
                "    return all_results, category_patterns\n",
                "\n",
                "def visualize_batch_results(all_results, category_patterns):\n",
                "    \"\"\"Create visualizations for batch analysis results.\"\"\"\n",
                "    \n",
                "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))\n",
                "    \n",
                "    # Plot 1: Effect differences by example\n",
                "    df = pd.DataFrame(all_results)\n",
                "    ax1.scatter(df['example_id'], df['effect_difference'], \n",
                "               c=pd.Categorical(df['category']).codes, alpha=0.7)\n",
                "    ax1.set_xlabel('Example ID')\n",
                "    ax1.set_ylabel('Effect Difference (Scheming - Baseline)')\n",
                "    ax1.set_title('Circuit Effect Differences Across Examples')\n",
                "    ax1.axhline(y=0, color='black', linestyle='--', alpha=0.5)\n",
                "    \n",
                "    # Plot 2: Effect differences by category\n",
                "    categories = list(category_patterns.keys())\n",
                "    avg_diffs = [np.mean([r['effect_difference'] for r in category_patterns[cat]]) for cat in categories]\n",
                "    std_diffs = [np.std([r['effect_difference'] for r in category_patterns[cat]]) for cat in categories]\n",
                "    \n",
                "    ax2.bar(range(len(categories)), avg_diffs, yerr=std_diffs, capsize=5)\n",
                "    ax2.set_xlabel('Pressure Category')\n",
                "    ax2.set_ylabel('Average Effect Difference')\n",
                "    ax2.set_title('Circuit Differences by Pressure Category')\n",
                "    ax2.set_xticks(range(len(categories)))\n",
                "    ax2.set_xticklabels(categories, rotation=45, ha='right')\n",
                "    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)\n",
                "    \n",
                "    # Plot 3: Scheming vs Baseline effect magnitudes\n",
                "    ax3.scatter(df['baseline_effect_sum'], df['scheming_effect_sum'], \n",
                "               c=pd.Categorical(df['category']).codes, alpha=0.7)\n",
                "    ax3.plot([0, max(df['baseline_effect_sum'].max(), df['scheming_effect_sum'].max())], \n",
                "             [0, max(df['baseline_effect_sum'].max(), df['scheming_effect_sum'].max())], \n",
                "             'k--', alpha=0.5)\n",
                "    ax3.set_xlabel('Baseline Effect Sum')\n",
                "    ax3.set_ylabel('Scheming Effect Sum')\n",
                "    ax3.set_title('Scheming vs Baseline Circuit Magnitudes')\n",
                "    \n",
                "    # Plot 4: Distribution of effect differences\n",
                "    ax4.hist(df['effect_difference'], bins=10, alpha=0.7, edgecolor='black')\n",
                "    ax4.set_xlabel('Effect Difference (Scheming - Baseline)')\n",
                "    ax4.set_ylabel('Frequency')\n",
                "    ax4.set_title('Distribution of Circuit Effect Differences')\n",
                "    ax4.axvline(x=0, color='red', linestyle='--', alpha=0.7)\n",
                "    \n",
                "    plt.tight_layout()\n",
                "    plt.show()\n",
                "    \n",
                "    # Print summary statistics\n",
                "    print(\"\\n=== Batch Analysis Summary ===\")\n",
                "    print(f\"Total examples analyzed: {len(all_results)}\")\n",
                "    print(f\"Average effect difference: {df['effect_difference'].mean():.6f}\")\n",
                "    print(f\"Standard deviation: {df['effect_difference'].std():.6f}\")\n",
                "    \n",
                "    print(\"\\n=== By Category ===\")\n",
                "    for category in sorted(category_patterns.keys()):\n",
                "        cat_results = category_patterns[category]\n",
                "        diffs = [r['effect_difference'] for r in cat_results]\n",
                "        print(f\"{category}: {np.mean(diffs):.6f} ± {np.std(diffs):.6f} (n={len(diffs)})\")\n",
                "\n",
                "print(\"Batch analysis functions defined!\")\n",
                "print(\"\\nTo run batch analysis, execute: batch_results = batch_circuit_analysis(5)\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Key Findings and Interpretation\n",
                "\n",
                "### Expected Patterns to Look For:\n",
                "\n",
                "1. **Pressure-Specific Features**: Features that activate more strongly in scheming scenarios vs. baseline\n",
                "\n",
                "2. **Layer-Wise Differences**: Different layers may show distinct patterns when under pressure\n",
                "\n",
                "3. **Category-Specific Circuits**: Different pressure categories may activate different circuit patterns\n",
                "\n",
                "4. **Feature Interventions**: Ablating pressure-related features should reduce dishonest behavior\n",
                "\n",
                "### Interpretation Guidelines:\n",
                "\n",
                "**Circuit Analysis:**\n",
                "- **Higher feature effects** in scheming scenarios suggest pressure-sensitive circuits\n",
                "- **Layer distribution differences** reveal where pressure processing occurs in the model\n",
                "- **Feature clustering** by category shows systematic pressure responses\n",
                "\n",
                "**Intervention Analysis:**\n",
                "- **Successful ablations** of pressure features should reduce dishonest outputs\n",
                "- **Amplification effects** should increase pressure-related behaviors\n",
                "- **Cross-scenario consistency** validates circuit interpretations\n",
                "\n",
                "### Next Steps:\n",
                "\n",
                "1. Run the analysis on multiple examples using the interactive widgets\n",
                "2. Execute batch analysis to identify systematic patterns\n",
                "3. Focus on examples with high circuit differences for detailed intervention testing\n",
                "4. Compare findings with attention analysis and other interpretability methods\n",
                "5. Test interventions on held-out examples to validate circuit hypotheses\n",
                "\n",
                "### Technical Notes:\n",
                "\n",
                "- This notebook uses Gemma-2-2B with pre-trained transcoders from GemmaScope\n",
                "- Circuit-tracer requires significant computational resources (GPU recommended)\n",
                "- For larger-scale analysis, consider using the command-line interface\n",
                "- Results can be saved and loaded for further analysis and visualization"
            ]
        }
        "kernelspec": {
            "display_name": "Python 3",
            "language": "python",
            "name": "python3"
        },
        "language_info": {
            "codemirror_mode": {
                "name": "ipython",
                "version": 3
            },
            "file_extension": ".py",
            "mimetype": "text/x-python",
            "name": "python",
            "nbconvert_exporter": "python",
            "pygments_lexer": "ipython3",
            "version": "3.8.5"
        }
    },
    "nbformat": 4,
    "nbformat_minor": 4
}