{"cells": [{"cell_type": "code", "execution_count": 8, "id": "b0333931", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries installed and imported successfully.\n"]}], "source": ["# @title 1. Setup and Imports\n", "# ---\n", "!pip install -q transformers accelerate bitsandbytes torch ipywidgets matplotlib bertviz pynvml\n", "\n", "import json, os, gc, warnings\n", "from collections import Counter\n", "import torch\n", "import transformers\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import pynvml\n", "import ipywidgets as widgets\n", "from IPython.display import display, clear_output, HTML\n", "import matplotlib.pyplot as plt\n", "from bertviz import model_view\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "print(\"✅ Libraries installed and imported successfully.\")\n", "try:\n", "    pynvml.nvmlInit()\n", "    GPU_AVAILABLE = True\n", "except pynvml.NVMLError:\n", "    GPU_AVAILABLE = False"]}, {"cell_type": "code", "execution_count": null, "id": "a12f9b09", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}