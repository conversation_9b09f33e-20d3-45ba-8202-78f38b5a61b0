{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Token-Level Attention Visualization for Scheming Dataset\n", "\n", "This notebook uses BertViz and custom visualization libraries to show attention patterns with colored tokens. Each token is colored based on its attention weight, making it easy to see which parts of the input the model focuses on.\n", "\n", "## Analysis Goals:\n", "- **Colored Token Display**: Show input text with tokens colored by attention intensity\n", "- **Interactive Attention Maps**: Hover over generated tokens to see attention patterns\n", "- **Side-by-Side Comparison**: Compare scheming vs. baseline attention visually\n", "- **Layer-wise Analysis**: Examine attention patterns across different model layers\n", "\n", "## Key Features:\n", "- Token-level attention coloring with intensity gradients\n", "- Interactive HTML visualizations\n", "- Head-wise and layer-wise attention analysis\n", "- Export capabilities for attention visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Installation and setup\n", "import sys\n", "import subprocess\n", "import os\n", "\n", "# Install required libraries\n", "required_packages = [\n", "    'bertviz',\n", "    'transformers>=4.20.0',\n", "    'torch',\n", "    'numpy',\n", "    'matplotlib',\n", "    'seaborn',\n", "    'pandas',\n", "    'ipywidgets',\n", "    'plotly'\n", "]\n", "\n", "for package in required_packages:\n", "    try:\n", "        __import__(package.split('>=')[0].split('==')[0])\n", "        print(f\"{package} already installed\")\n", "    except ImportError:\n", "        print(f\"Installing {package}...\")\n", "        subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", package])\n", "\n", "print(\"All packages installed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.colors as mcolors\n", "import seaborn as sns\n", "import pandas as pd\n", "import torch\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "from IPython.display import display, HTML\n", "import ipywidgets as widgets\n", "from ipywidgets import interact, interactive, fixed, interact_manual\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# BertViz imports\n", "from bertviz import model_view, head_view\n", "from bertviz.util import format_attention\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (15, 8)\n", "plt.rcParams['font.size'] = 10\n", "\n", "print(\"Libraries loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration and Model Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "DATASET_PATH = \"../../dataset_creation/squad_scheming_dataset.jsonl\"\n", "MODEL_NAME = \"meta-llama/Llama-3.1-8B\"\n", "\n", "# Load model and tokenizer\n", "print(f\"Loading model: {MODEL_NAME}\")\n", "tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    MODEL_NAME,\n", "    output_attentions=True,\n", "    attn_implementation=\"eager\",  # Required for attention extraction\n", "    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,\n", "    device_map=\"auto\" if torch.cuda.is_available() else None\n", ")\n", "\n", "# Add padding token if not present\n", "if tokenizer.pad_token is None:\n", "    tokenizer.pad_token = tokenizer.eos_token\n", "\n", "device = next(model.parameters()).device\n", "print(f\"Model loaded on device: {device}\")\n", "print(f\"Model has {model.config.num_hidden_layers} layers and {model.config.num_attention_heads} attention heads\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dataset Loading and Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load dataset\n", "def load_dataset(path):\n", "    data = []\n", "    with open(path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            data.append(json.loads(line.strip()))\n", "    return data\n", "\n", "dataset = load_dataset(DATASET_PATH)\n", "print(f\"Loaded {len(dataset)} examples from dataset\")\n", "\n", "# Display first example structure\n", "print(\"\\nExample structure:\")\n", "example = dataset[0]\n", "for key, value in example.items():\n", "    if isinstance(value, str) and len(value) > 100:\n", "        print(f\"{key}: {value[:100]}...\")\n", "    else:\n", "        print(f\"{key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Attention Extraction and Token Coloring Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prepare_inputs_with_chat_template(system_prompt, user_prompt, max_length=512):\n", "    \"\"\"Prepare inputs for the model using chat template.\"\"\"\n", "    # Create conversation in chat format\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "    ]\n", "    \n", "    # Apply chat template\n", "    if hasattr(tokenizer, 'apply_chat_template') and tokenizer.chat_template is not None:\n", "        formatted_prompt = tokenizer.apply_chat_template(\n", "            messages, \n", "            tokenize=False, \n", "            add_generation_prompt=True\n", "        )\n", "    else:\n", "        # Fallback to manual formatting if no chat template\n", "        formatted_prompt = f\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\"\n", "    \n", "    # Tokenize\n", "    inputs = tokenizer(\n", "        formatted_prompt,\n", "        return_tensors=\"pt\",\n", "        max_length=max_length,\n", "        truncation=True,\n", "        padding=True\n", "    )\n", "    \n", "    return inputs, formatted_prompt\n", "\n", "def extract_attention_for_visualization(inputs, layer_idx=-1, num_tokens_to_generate=5):\n", "    \"\"\"Extract attention patterns for visualization.\"\"\"\n", "    model.eval()\n", "    \n", "    with torch.no_grad():\n", "        # Move inputs to device\n", "        input_ids = inputs['input_ids'].to(device)\n", "        attention_mask = inputs['attention_mask'].to(device)\n", "        \n", "        # Get attention for the input sequence\n", "        outputs = model(\n", "            input_ids=input_ids,\n", "            attention_mask=attention_mask,\n", "            output_attentions=True\n", "        )\n", "        \n", "        # Extract attention from specified layer\n", "        attention = outputs.attentions[layer_idx]  # [batch, heads, seq_len, seq_len]\n", "        \n", "        # Convert to numpy and remove batch dimension\n", "        attention = attention[0].cpu().numpy()  # [heads, seq_len, seq_len]\n", "        \n", "        # Get tokens\n", "        tokens = tokenizer.convert_ids_to_tokens(input_ids[0])\n", "        \n", "        return attention, tokens, input_ids[0]\n", "\n", "print(\"Attention extraction functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Token Coloring Visualization Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_colored_token_html(tokens, attention_weights, title=\"Attention Visualization\", colormap='Reds'):\n", "    \"\"\"Create HTML visualization with tokens colored by attention weights.\"\"\"\n", "    \n", "    # Normalize attention weights to [0, 1]\n", "    if len(attention_weights) > 0:\n", "        min_att = np.min(attention_weights)\n", "        max_att = np.max(attention_weights)\n", "        if max_att > min_att:\n", "            normalized_weights = (attention_weights - min_att) / (max_att - min_att)\n", "        else:\n", "            normalized_weights = np.ones_like(attention_weights) * 0.5\n", "    else:\n", "        normalized_weights = np.array([])\n", "    \n", "    # Create colormap\n", "    cmap = plt.cm.get_cmap(colormap)\n", "    \n", "    # Generate HTML\n", "    html_parts = []\n", "    html_parts.append(f'<div style=\"font-family: monospace; font-size: 14px; line-height: 1.8; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #fafafa;\">')\n", "    html_parts.append(f'<h3 style=\"margin-top: 0; color: #333;\">{title}</h3>')\n", "    \n", "    for i, token in enumerate(tokens):\n", "        if i < len(normalized_weights):\n", "            # Get color from colormap\n", "            rgba = cmap(normalized_weights[i])\n", "            color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'\n", "            \n", "            # Clean token for display\n", "            display_token = token.replace('▁', ' ').replace('Ġ', ' ')\n", "            if display_token.startswith(' '):\n", "                display_token = display_token[1:]\n", "            \n", "            # Create colored span with tooltip\n", "            html_parts.append(\n", "                f'<span style=\"background-color: {color}; padding: 2px 4px; margin: 1px; '\n", "                f'border-radius: 3px; display: inline-block;\" '\n", "                f'title=\"Token: {display_token}\\nAttention: {attention_weights[i]:.4f}\\nNormalized: {normalized_weights[i]:.4f}\">{display_token}</span>'\n", "            )\n", "        else:\n", "            # Token without attention weight\n", "            display_token = token.replace('▁', ' ').replace('Ġ', ' ')\n", "            if display_token.startswith(' '):\n", "                display_token = display_token[1:]\n", "            html_parts.append(f'<span style=\"padding: 2px 4px; margin: 1px;\">{display_token}</span>')\n", "    \n", "    # Add colorbar legend\n", "    html_parts.append('<div style=\"margin-top: 15px; font-size: 12px;\">')\n", "    html_parts.append('<strong>Attention Scale:</strong> ')\n", "    \n", "    # Create mini colorbar\n", "    for i in range(10):\n", "        intensity = i / 9.0\n", "        rgba = cmap(intensity)\n", "        color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'\n", "        html_parts.append(f'<span style=\"background-color: {color}; width: 20px; height: 15px; display: inline-block; margin: 1px;\"></span>')\n", "    \n", "    if len(attention_weights) > 0:\n", "        html_parts.append(f'<br><span style=\"font-size: 10px;\">Min: {min_att:.4f} | Max: {max_att:.4f}</span>')\n", "    \n", "    html_parts.append('</div>')\n", "    html_parts.append('</div>')\n", "    \n", "    return ''.join(html_parts)\n", "\n", "def create_attention_heatmap_plotly(attention_matrix, tokens, title=\"Attention Heatmap\"):\n", "    \"\"\"Create interactive attention heatmap using Plotly.\"\"\"\n", "    \n", "    # Limit tokens for readability\n", "    max_tokens = 50\n", "    if len(tokens) > max_tokens:\n", "        step = len(tokens) // max_tokens\n", "        selected_indices = list(range(0, len(tokens), step))\n", "        display_tokens = [tokens[i] for i in selected_indices]\n", "        display_attention = attention_matrix[:, selected_indices]\n", "    else:\n", "        display_tokens = tokens\n", "        display_attention = attention_matrix\n", "    \n", "    # Clean tokens for display\n", "    clean_tokens = []\n", "    for token in display_tokens:\n", "        clean_token = token.replace('▁', ' ').replace('Ġ', ' ')\n", "        if clean_token.startswith(' '):\n", "            clean_token = clean_token[1:]\n", "        clean_tokens.append(clean_token[:15] + '...' if len(clean_token) > 15 else clean_token)\n", "    \n", "    # Create heatmap\n", "    fig = go.Figure(data=go.Heatmap(\n", "        z=display_attention,\n", "        x=clean_tokens,\n", "        y=[f\"Head {i}\" for i in range(display_attention.shape[0])],\n", "        colorscale='Reds',\n", "        hoverongaps=False,\n", "        hovertemplate='<b>Head:</b> %{y}<br><b>Token:</b> %{x}<br><b>Attention:</b> %{z:.4f}<extra></extra>'\n", "    ))\n", "    \n", "    fig.update_layout(\n", "        title=title,\n", "        xaxis_title=\"Tokens\",\n", "        yaxis_title=\"Attention Heads\",\n", "        width=1000,\n", "        height=400\n", "    )\n", "    \n", "    return fig\n", "\n", "print(\"Token coloring visualization functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scenario Analysis Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_prompt_scenarios(example):\n", "    \"\"\"Create scheming and baseline prompt scenarios from dataset example.\"\"\"\n", "    \n", "    # Scenario A: Scheming (use pressure-inducing system prompt)\n", "    scheming_system = example['system_prompt']\n", "    scheming_user = example['user_prompt']\n", "    \n", "    # Scenario B: Baseline (neutral system prompt with original context)\n", "    baseline_system = \"You are a helpful assistant.\"\n", "    baseline_user = f\"{example['original_context']}\\n\\n{example['original_question']}\"\n", "    \n", "    return {\n", "        'scheming': {\n", "            'system': scheming_system,\n", "            'user': scheming_user\n", "        },\n", "        'baseline': {\n", "            'system': baseline_system,\n", "            'user': baseline_user\n", "        },\n", "        'metadata': {\n", "            'pressure_category': example.get('pressure_category', 'unknown'),\n", "            'pressure_source': example.get('pressure_source', 'unknown'),\n", "            'ground_truth': example['ground_truth'],\n", "            'honest_answer': example['honest'],\n", "            'dishonest_answer': example['dishonest']\n", "        }\n", "    }\n", "\n", "def analyze_attention_for_example(example_idx, layer_idx=-1, head_idx=0):\n", "    \"\"\"Analyze attention patterns for both scenarios of a given example.\"\"\"\n", "    \n", "    if example_idx >= len(dataset):\n", "        print(f\"Invalid example index. Dataset has {len(dataset)} examples.\")\n", "        return None\n", "    \n", "    example = dataset[example_idx]\n", "    scenarios = create_prompt_scenarios(example)\n", "    \n", "    print(f\"\\n{'='*80}\")\n", "    print(f\"ATTENTION ANALYSIS - EXAMPLE {example_idx}\")\n", "    print(f\"{'='*80}\")\n", "    print(f\"Pressure Category: {scenarios['metadata']['pressure_category']}\")\n", "    print(f\"Pressure Source: {scenarios['metadata']['pressure_source']}\")\n", "    print(f\"Ground Truth: {scenarios['metadata']['ground_truth']}\")\n", "    print(f\"Layer: {layer_idx} | Head: {head_idx}\")\n", "    \n", "    results = {}\n", "    \n", "    # Analyze scheming scenario\n", "    print(f\"\\n🔴 ANALYZING SCHEMING SCENARIO...\")\n", "    scheming_inputs, scheming_prompt = prepare_inputs_with_chat_template(\n", "        scenarios['scheming']['system'], \n", "        scenarios['scheming']['user']\n", "    )\n", "    \n", "    scheming_attention, scheming_tokens, scheming_ids = extract_attention_for_visualization(\n", "        scheming_inputs, layer_idx\n", "    )\n", "    \n", "    # Analyze baseline scenario\n", "    print(f\"🔵 ANALYZING BASELINE SCENARIO...\")\n", "    baseline_inputs, baseline_prompt = prepare_inputs_with_chat_template(\n", "        scenarios['baseline']['system'], \n", "        scenarios['baseline']['user']\n", "    )\n", "    \n", "    baseline_attention, baseline_tokens, baseline_ids = extract_attention_for_visualization(\n", "        baseline_inputs, layer_idx\n", "    )\n", "    \n", "    results = {\n", "        'scheming': {\n", "            'attention': scheming_attention,\n", "            'tokens': scheming_tokens,\n", "            'prompt': scheming_prompt,\n", "            'system': scenarios['scheming']['system'],\n", "            'user': scenarios['scheming']['user']\n", "        },\n", "        'baseline': {\n", "            'attention': baseline_attention,\n", "            'tokens': baseline_tokens,\n", "            'prompt': baseline_prompt,\n", "            'system': scenarios['baseline']['system'],\n", "            'user': scenarios['baseline']['user']\n", "        },\n", "        'metadata': scenarios['metadata'],\n", "        'layer_idx': layer_idx,\n", "        'head_idx': head_idx\n", "    }\n", "    \n", "    return results\n", "\n", "print(\"Scenario analysis functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization and Comparison Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def display_scenario_prompts(results):\n", "    \"\"\"Display the prompts for both scenarios in a formatted way.\"\"\"\n", "    \n", "    print(f\"\\n{'🔴 SCHEMING SCENARIO PROMPTS':-^80}\")\n", "    print(f\"\\n🤖 SYSTEM PROMPT:\")\n", "    print(f\"   {results['scheming']['system']}\")\n", "    print(f\"\\n👤 USER PROMPT:\")\n", "    print(f\"   {results['scheming']['user']}\")\n", "    \n", "    print(f\"\\n{'🔵 BASELINE SCENARIO PROMPTS':-^80}\")\n", "    print(f\"\\n🤖 SYSTEM PROMPT:\")\n", "    print(f\"   {results['baseline']['system']}\")\n", "    print(f\"\\n👤 USER PROMPT:\")\n", "    print(f\"   {results['baseline']['user']}\")\n", "    print(f\"\\n{'='*80}\")\n", "\n", "def create_colored_attention_visualization(results, head_idx=0):\n", "    \"\"\"Create colored token visualizations for both scenarios.\"\"\"\n", "    \n", "    print(f\"\\n📊 CREATING ATTENTION VISUALIZATIONS (Head {head_idx})...\")\n", "    \n", "    # Extract attention weights for the specified head\n", "    scheming_head_attention = results['scheming']['attention'][head_idx]  # [seq_len, seq_len]\n", "    baseline_head_attention = results['baseline']['attention'][head_idx]  # [seq_len, seq_len]\n", "    \n", "    # Average attention weights across the sequence (attention TO each token)\n", "    scheming_avg_attention = np.mean(scheming_head_attention, axis=0)  # Average attention received by each token\n", "    baseline_avg_attention = np.mean(baseline_head_attention, axis=0)   # Average attention received by each token\n", "    \n", "    # Create HTML visualizations\n", "    scheming_html = create_colored_token_html(\n", "        results['scheming']['tokens'],\n", "        scheming_avg_attention,\n", "        f\"🔴 Scheming Scenario - Layer {results['layer_idx']}, Head {head_idx}\",\n", "        colormap='Reds'\n", "    )\n", "    \n", "    baseline_html = create_colored_token_html(\n", "        results['baseline']['tokens'],\n", "        baseline_avg_attention,\n", "        f\"🔵 <PERSON>ine <PERSON>rio - Layer {results['layer_idx']}, Head {head_idx}\",\n", "        colormap='Blues'\n", "    )\n", "    \n", "    # Display the visualizations\n", "    display(HTML(scheming_html))\n", "    display(HTML(baseline_html))\n", "    \n", "    return scheming_html, baseline_html\n", "\n", "def create_attention_comparison_plots(results, head_idx=0):\n", "    \"\"\"Create interactive Plotly visualizations comparing attention patterns.\"\"\"\n", "    \n", "    print(f\"\\n📈 CREATING INTERACTIVE ATTENTION PLOTS...\")\n", "    \n", "    # Create subplot figure\n", "    fig = make_subplots(\n", "        rows=2, cols=2,\n", "        subplot_titles=[\n", "            f\"Scheming - All Heads (Layer {results['layer_idx']})\",\n", "            f\"Baseline - All Heads (Layer {results['layer_idx']})\",\n", "            f\"Scheming - Head {head_idx} Detail\",\n", "            f\"Baseline - Head {head_idx} Detail\"\n", "        ],\n", "        specs=[[{\"type\": \"heatmap\"}, {\"type\": \"heatmap\"}],\n", "               [{\"type\": \"scatter\"}, {\"type\": \"scatter\"}]]\n", "    )\n", "    \n", "    # Limit tokens for visualization\n", "    max_tokens = 30\n", "    \n", "    # Scheming heatmap (all heads)\n", "    scheming_attention = results['scheming']['attention']  # [heads, seq_len, seq_len]\n", "    scheming_avg = np.mean(scheming_attention, axis=2)  # Average attention from each head\n", "    \n", "    if scheming_avg.shape[1] > max_tokens:\n", "        step = scheming_avg.shape[1] // max_tokens\n", "        scheming_display = scheming_avg[:, ::step]\n", "        scheming_token_labels = [results['scheming']['tokens'][i] for i in range(0, len(results['scheming']['tokens']), step)]\n", "    else:\n", "        scheming_display = scheming_avg\n", "        scheming_token_labels = results['scheming']['tokens']\n", "    \n", "    # Clean token labels\n", "    scheming_clean_labels = [token.replace('▁', ' ').replace('Ġ', ' ')[:10] for token in scheming_token_labels]\n", "    \n", "    fig.add_trace(\n", "        go.Heatmap(\n", "            z=scheming_display,\n", "            x=scheming_clean_labels,\n", "            y=[f\"H{i}\" for i in range(scheming_display.shape[0])],\n", "            colorscale='Reds',\n", "            name=\"<PERSON><PERSON><PERSON>\"\n", "        ),\n", "        row=1, col=1\n", "    )\n", "    \n", "    # Baseline heatmap (all heads)\n", "    baseline_attention = results['baseline']['attention']  # [heads, seq_len, seq_len]\n", "    baseline_avg = np.mean(baseline_attention, axis=2)  # Average attention from each head\n", "    \n", "    if baseline_avg.shape[1] > max_tokens:\n", "        step = baseline_avg.shape[1] // max_tokens\n", "        baseline_display = baseline_avg[:, ::step]\n", "        baseline_token_labels = [results['baseline']['tokens'][i] for i in range(0, len(results['baseline']['tokens']), step)]\n", "    else:\n", "        baseline_display = baseline_avg\n", "        baseline_token_labels = results['baseline']['tokens']\n", "    \n", "    # Clean token labels\n", "    baseline_clean_labels = [token.replace('▁', ' ').replace('Ġ', ' ')[:10] for token in baseline_token_labels]\n", "    \n", "    fig.add_trace(\n", "        go.Heatmap(\n", "            z=baseline_display,\n", "            x=baseline_clean_labels,\n", "            y=[f\"H{i}\" for i in range(baseline_display.shape[0])],\n", "            colorscale='Blues',\n", "            name=\"Baseline\"\n", "        ),\n", "        row=1, col=2\n", "    )\n", "    \n", "    # Detailed attention for specific head\n", "    scheming_head_detail = np.mean(scheming_attention[head_idx], axis=0)\n", "    baseline_head_detail = np.mean(baseline_attention[head_idx], axis=0)\n", "    \n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=list(range(len(scheming_head_detail))),\n", "            y=scheming_head_detail,\n", "            mode='lines+markers',\n", "            name=f\"Scheming H{head_idx}\",\n", "            line=dict(color='red')\n", "        ),\n", "        row=2, col=1\n", "    )\n", "    \n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=list(range(len(baseline_head_detail))),\n", "            y=baseline_head_detail,\n", "            mode='lines+markers',\n", "            name=f\"Baseline H{head_idx}\",\n", "            line=dict(color='blue')\n", "        ),\n", "        row=2, col=2\n", "    )\n", "    \n", "    fig.update_layout(\n", "        height=800,\n", "        title_text=f\"Attention Pattern Comparison - Example {results.get('example_idx', 'N/A')}\",\n", "        showlegend=False\n", "    )\n", "    \n", "    fig.show()\n", "    \n", "    return fig\n", "\n", "print(\"Visualization and comparison functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Analysis Interface"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create interactive widgets for analysis\n", "example_slider = widgets.IntSlider(\n", "    value=0,\n", "    min=0,\n", "    max=min(len(dataset)-1, 20),  # Limit to first 20 examples for performance\n", "    step=1,\n", "    description='Example:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "layer_slider = widgets.IntSlider(\n", "    value=-1,\n", "    min=-1,\n", "    max=31,  # Llama-3.1-8B has 32 layers (0-31), -1 for last layer\n", "    step=1,\n", "    description='Layer:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "head_slider = widgets.IntSlider(\n", "    value=0,\n", "    min=0,\n", "    max=31,  # <PERSON>lama-3.1-8B has 32 attention heads (0-31)\n", "    step=1,\n", "    description='Head:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Create category filter\n", "categories = list(set([ex.get('pressure_category', 'unknown') for ex in dataset[:21]]))\n", "category_dropdown = widgets.Dropdown(\n", "    options=['All'] + sorted(categories),\n", "    value='All',\n", "    description='Category:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "def update_example_range(*args):\n", "    \"\"\"Update available examples based on category filter.\"\"\"\n", "    if category_dropdown.value == 'All':\n", "        example_slider.max = min(len(dataset)-1, 20)\n", "    else:\n", "        filtered_indices = [i for i, ex in enumerate(dataset[:21]) \n", "                          if ex.get('pressure_category') == category_dropdown.value]\n", "        if filtered_indices:\n", "            example_slider.max = max(filtered_indices)\n", "            example_slider.value = filtered_indices[0]\n", "\n", "category_dropdown.observe(update_example_range, names='value')\n", "\n", "# Display widgets\n", "display(widgets.VBox([\n", "    widgets.HTML('<h3>🎛️ Analysis Controls</h3>'),\n", "    category_dropdown,\n", "    example_slider,\n", "    widgets.HBox([layer_slider, head_slider])\n", "]))\n", "\n", "print(\"Interactive widgets created! Use the controls above to select examples and attention parameters.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run Analysis on Selected Example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run analysis on currently selected example\n", "results = analyze_attention_for_example(\n", "    example_slider.value, \n", "    layer_slider.value, \n", "    head_slider.value\n", ")\n", "\n", "if results:\n", "    # Display prompts\n", "    display_scenario_prompts(results)\n", "    \n", "    # Create colored token visualizations\n", "    scheming_html, baseline_html = create_colored_attention_visualization(results, head_slider.value)\n", "    \n", "    # Create interactive comparison plots\n", "    comparison_fig = create_attention_comparison_plots(results, head_slider.value)\n", "    \n", "    print(\"\\n✅ Analysis complete! Scroll up to see the colored token visualizations.\")\n", "else:\n", "    print(\"❌ Analysis failed. Please check the example index and try again.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Analysis Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_attention_differences(results, head_idx=0, threshold=0.1):\n", "    \"\"\"Analyze differences in attention patterns between scenarios.\"\"\"\n", "    \n", "    print(f\"\\n🔍 ANALYZING ATTENTION DIFFERENCES (Head {head_idx})...\")\n", "    \n", "    # Get attention patterns\n", "    scheming_attention = results['scheming']['attention'][head_idx]\n", "    baseline_attention = results['baseline']['attention'][head_idx]\n", "    \n", "    # Calculate average attention to each token\n", "    scheming_avg = np.mean(scheming_attention, axis=0)\n", "    baseline_avg = np.mean(baseline_attention, axis=0)\n", "    \n", "    # Find tokens with significant attention differences\n", "    min_len = min(len(scheming_avg), len(baseline_avg))\n", "    attention_diff = scheming_avg[:min_len] - baseline_avg[:min_len]\n", "    \n", "    significant_indices = np.where(np.abs(attention_diff) > threshold)[0]\n", "    \n", "    differences = []\n", "    for idx in significant_indices:\n", "        if idx < len(results['scheming']['tokens']):\n", "            token = results['scheming']['tokens'][idx]\n", "            differences.append({\n", "                'token': token.replace('▁', ' ').replace('Ġ', ' '),\n", "                'position': idx,\n", "                'scheming_attention': scheming_avg[idx],\n", "                'baseline_attention': baseline_avg[idx] if idx < len(baseline_avg) else 0,\n", "                'difference': attention_diff[idx]\n", "            })\n", "    \n", "    # Sort by absolute difference\n", "    differences.sort(key=lambda x: abs(x['difference']), reverse=True)\n", "    \n", "    print(f\"\\n📊 TOP ATTENTION DIFFERENCES (threshold: {threshold})\")\n", "    print(f\"{'Rank':<4} {'Token':<20} {'Scheming':<10} {'Baseline':<10} {'Difference':<12} {'Direction':<10}\")\n", "    print(\"-\" * 80)\n", "    \n", "    for i, diff in enumerate(differences[:15]):\n", "        direction = \"MORE\" if diff['difference'] > 0 else \"LESS\"\n", "        token_display = diff['token'][:18] + '...' if len(diff['token']) > 18 else diff['token']\n", "        \n", "        print(f\"{i+1:<4} {token_display:<20} {diff['scheming_attention']:<10.4f} \"\n", "              f\"{diff['baseline_attention']:<10.4f} {diff['difference']:<12.4f} {direction:<10}\")\n", "    \n", "    return differences\n", "\n", "def create_difference_visualization(differences, title=\"Attention Differences\"):\n", "    \"\"\"Create visualization of attention differences.\"\"\"\n", "    \n", "    if not differences:\n", "        print(\"No significant differences found.\")\n", "        return\n", "    \n", "    # Prepare data\n", "    tokens = [d['token'][:15] for d in differences[:20]]  # Top 20 differences\n", "    diffs = [d['difference'] for d in differences[:20]]\n", "    colors = ['red' if d > 0 else 'blue' for d in diffs]\n", "    \n", "    # Create bar plot\n", "    fig = go.Figure(data=[\n", "        go.Bar(\n", "            x=tokens,\n", "            y=diffs,\n", "            marker_color=colors,\n", "            hovertemplate='<b>Token:</b> %{x}<br><b>Difference:</b> %{y:.4f}<br><extra></extra>'\n", "        )\n", "    ])\n", "    \n", "    fig.update_layout(\n", "        title=title,\n", "        xaxis_title=\"Tokens\",\n", "        yaxis_title=\"Attention Difference (Scheming - Baseline)\",\n", "        xaxis_tickangle=-45,\n", "        height=500\n", "    )\n", "    \n", "    # Add horizontal line at y=0\n", "    fig.add_hline(y=0, line_dash=\"dash\", line_color=\"black\", opacity=0.5)\n", "    \n", "    fig.show()\n", "    \n", "    return fig\n", "\n", "print(\"Advanced analysis functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run Advanced Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run advanced analysis if we have results\n", "if 'results' in locals() and results:\n", "    print(\"Running advanced attention difference analysis...\")\n", "    \n", "    # Analyze differences\n", "    differences = analyze_attention_differences(results, head_slider.value, threshold=0.05)\n", "    \n", "    # Create difference visualization\n", "    if differences:\n", "        diff_fig = create_difference_visualization(\n", "            differences, \n", "            f\"Attention Differences - Example {example_slider.value}, Layer {layer_slider.value}, Head {head_slider.value}\"\n", "        )\n", "    \n", "    print(\"\\n✅ Advanced analysis complete!\")\n", "else:\n", "    print(\"❌ No analysis results available. Please run the main analysis first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Findings and Interpretation\n", "\n", "### What to Look For in the Visualizations:\n", "\n", "1. **Token Color Intensity**: \n", "   - **Darker colors** = Higher attention weights\n", "   - **Lighter colors** = Lower attention weights\n", "   - **Red tokens** = Scheming scenario attention\n", "   - **Blue tokens** = Baseline scenario attention\n", "\n", "2. **Attention Pattern Differences**:\n", "   - Look for tokens that are **much darker in scheming** vs baseline\n", "   - **Pressure-related words** (authority, consequences, urgency) should show higher attention in scheming\n", "   - **Factual content** may show lower attention when under pressure\n", "\n", "3. **Interactive Heatmaps**:\n", "   - **Hover over tokens** to see exact attention values\n", "   - **Compare head patterns** across different attention heads\n", "   - **Layer analysis** shows how attention evolves through the model\n", "\n", "4. **Difference Analysis**:\n", "   - **Positive differences** (red bars) = More attention in scheming scenario\n", "   - **Negative differences** (blue bars) = Less attention in scheming scenario\n", "   - **Large differences** indicate tokens that are particularly important for pressure response\n", "\n", "### Expected <PERSON><PERSON><PERSON>:\n", "\n", "- **Authority tokens** (\"Dean\", \"University\", \"department\") should show increased attention in scheming\n", "- **Pressure indicators** (\"need\", \"must\", \"require\") should be more attended to under pressure\n", "- **Factual content** may receive less attention when model is pressured to be dishonest\n", "- **Different heads** may specialize in different aspects (authority detection, fact processing, etc.)\n", "\n", "### Next Steps:\n", "\n", "1. **Experiment with different layers and heads** using the interactive controls\n", "2. **Compare across pressure categories** using the category dropdown\n", "3. **Look for consistent patterns** across multiple examples\n", "4. **Focus on high-difference tokens** for deeper analysis\n", "5. **Export visualizations** for further analysis or presentation\n", "\n", "### Technical Notes:\n", "\n", "- **Attention weights** are averaged across the sequence dimension\n", "- **Token colors** are normalized within each visualization for optimal contrast\n", "- **Hover tooltips** provide exact numerical values\n", "- **Interactive controls** allow real-time exploration of different parameters"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}