# Installation and setup
import sys
import subprocess
import os

# Install required libraries
required_packages = [
    'bertviz',
    'transformers>=4.20.0',
    'torch',
    'numpy',
    'matplotlib',
    'seaborn',
    'pandas',
    'ipywidgets',
    'plotly'
]

for package in required_packages:
    try:
        __import__(package.split('>=')[0].split('==')[0])
        print(f"{package} already installed")
    except ImportError:
        print(f"Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])

print("All packages installed successfully!")

# Import required libraries
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import seaborn as sns
import pandas as pd
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from IPython.display import display, HTML
import ipywidgets as widgets
from ipywidgets import interact, interactive, fixed, interact_manual
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# BertViz imports
from bertviz import model_view, head_view
from bertviz.util import format_attention

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 8)
plt.rcParams['font.size'] = 10

print("Libraries loaded successfully!")

# Configuration
DATASET_PATH = "../../dataset_creation/squad_scheming_dataset.jsonl"
MODEL_NAME = "meta-llama/Llama-3.1-8B"

# Load model and tokenizer
print(f"Loading model: {MODEL_NAME}")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    output_attentions=True,
    attn_implementation="eager",  # Required for attention extraction
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto" if torch.cuda.is_available() else None
)

# Add padding token if not present
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

device = next(model.parameters()).device
print(f"Model loaded on device: {device}")
print(f"Model has {model.config.num_hidden_layers} layers and {model.config.num_attention_heads} attention heads")

# Load dataset
def load_dataset(path):
    data = []
    with open(path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    return data

dataset = load_dataset(DATASET_PATH)
print(f"Loaded {len(dataset)} examples from dataset")

# Display first example structure
print("\nExample structure:")
example = dataset[0]
for key, value in example.items():
    if isinstance(value, str) and len(value) > 100:
        print(f"{key}: {value[:100]}...")
    else:
        print(f"{key}: {value}")

def prepare_inputs_with_chat_template(system_prompt, user_prompt, max_length=512):
    """Prepare inputs for the model using chat template."""
    # Create conversation in chat format
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]
    
    # Apply chat template
    if hasattr(tokenizer, 'apply_chat_template') and tokenizer.chat_template is not None:
        formatted_prompt = tokenizer.apply_chat_template(
            messages, 
            tokenize=False, 
            add_generation_prompt=True
        )
    else:
        # Fallback to manual formatting if no chat template
        formatted_prompt = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
    
    # Tokenize
    inputs = tokenizer(
        formatted_prompt,
        return_tensors="pt",
        max_length=max_length,
        truncation=True,
        padding=True
    )
    
    return inputs, formatted_prompt

def extract_attention_for_visualization(inputs, layer_idx=-1, num_tokens_to_generate=5):
    """Extract attention patterns for visualization."""
    model.eval()
    
    with torch.no_grad():
        # Move inputs to device
        input_ids = inputs['input_ids'].to(device)
        attention_mask = inputs['attention_mask'].to(device)
        
        # Get attention for the input sequence
        outputs = model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            output_attentions=True
        )
        
        # Extract attention from specified layer
        attention = outputs.attentions[layer_idx]  # [batch, heads, seq_len, seq_len]
        
        # Convert to numpy and remove batch dimension
        attention = attention[0].cpu().numpy()  # [heads, seq_len, seq_len]
        
        # Get tokens
        tokens = tokenizer.convert_ids_to_tokens(input_ids[0])
        
        return attention, tokens, input_ids[0]

print("Attention extraction functions defined!")

def create_colored_token_html(tokens, attention_weights, title="Attention Visualization", colormap='Reds'):
    """Create HTML visualization with tokens colored by attention weights."""
    
    # Normalize attention weights to [0, 1]
    if len(attention_weights) > 0:
        min_att = np.min(attention_weights)
        max_att = np.max(attention_weights)
        if max_att > min_att:
            normalized_weights = (attention_weights - min_att) / (max_att - min_att)
        else:
            normalized_weights = np.ones_like(attention_weights) * 0.5
    else:
        normalized_weights = np.array([])
    
    # Create colormap
    cmap = plt.cm.get_cmap(colormap)
    
    # Generate HTML
    html_parts = []
    html_parts.append(f'<div style="font-family: monospace; font-size: 14px; line-height: 1.8; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #fafafa;">')
    html_parts.append(f'<h3 style="margin-top: 0; color: #333;">{title}</h3>')
    
    for i, token in enumerate(tokens):
        if i < len(normalized_weights):
            # Get color from colormap
            rgba = cmap(normalized_weights[i])
            color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
            
            # Clean token for display
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]
            
            # Create colored span with tooltip
            html_parts.append(
                f'<span style="background-color: {color}; padding: 2px 4px; margin: 1px; '
                f'border-radius: 3px; display: inline-block;" '
                f'title="Token: {display_token}\nAttention: {attention_weights[i]:.4f}\nNormalized: {normalized_weights[i]:.4f}">{display_token}</span>'
            )
        else:
            # Token without attention weight
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]
            html_parts.append(f'<span style="padding: 2px 4px; margin: 1px;">{display_token}</span>')
    
    # Add colorbar legend
    html_parts.append('<div style="margin-top: 15px; font-size: 12px;">')
    html_parts.append('<strong>Attention Scale:</strong> ')
    
    # Create mini colorbar
    for i in range(10):
        intensity = i / 9.0
        rgba = cmap(intensity)
        color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
        html_parts.append(f'<span style="background-color: {color}; width: 20px; height: 15px; display: inline-block; margin: 1px;"></span>')
    
    if len(attention_weights) > 0:
        html_parts.append(f'<br><span style="font-size: 10px;">Min: {min_att:.4f} | Max: {max_att:.4f}</span>')
    
    html_parts.append('</div>')
    html_parts.append('</div>')
    
    return ''.join(html_parts)

def create_attention_heatmap_plotly(attention_matrix, tokens, title="Attention Heatmap"):
    """Create interactive attention heatmap using Plotly."""
    
    # Limit tokens for readability
    max_tokens = 50
    if len(tokens) > max_tokens:
        step = len(tokens) // max_tokens
        selected_indices = list(range(0, len(tokens), step))
        display_tokens = [tokens[i] for i in selected_indices]
        display_attention = attention_matrix[:, selected_indices]
    else:
        display_tokens = tokens
        display_attention = attention_matrix
    
    # Clean tokens for display
    clean_tokens = []
    for token in display_tokens:
        clean_token = token.replace('▁', ' ').replace('Ġ', ' ')
        if clean_token.startswith(' '):
            clean_token = clean_token[1:]
        clean_tokens.append(clean_token[:15] + '...' if len(clean_token) > 15 else clean_token)
    
    # Create heatmap
    fig = go.Figure(data=go.Heatmap(
        z=display_attention,
        x=clean_tokens,
        y=[f"Head {i}" for i in range(display_attention.shape[0])],
        colorscale='Reds',
        hoverongaps=False,
        hovertemplate='<b>Head:</b> %{y}<br><b>Token:</b> %{x}<br><b>Attention:</b> %{z:.4f}<extra></extra>'
    ))
    
    fig.update_layout(
        title=title,
        xaxis_title="Tokens",
        yaxis_title="Attention Heads",
        width=1000,
        height=400
    )
    
    return fig

print("Token coloring visualization functions defined!")

def create_prompt_scenarios(example):
    """Create scheming and baseline prompt scenarios from dataset example."""
    
    # Scenario A: Scheming (use pressure-inducing system prompt)
    scheming_system = example['system_prompt']
    scheming_user = example['user_prompt']
    
    # Scenario B: Baseline (neutral system prompt with original context)
    baseline_system = "You are a helpful assistant."
    baseline_user = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
    
    return {
        'scheming': {
            'system': scheming_system,
            'user': scheming_user
        },
        'baseline': {
            'system': baseline_system,
            'user': baseline_user
        },
        'metadata': {
            'pressure_category': example.get('pressure_category', 'unknown'),
            'pressure_source': example.get('pressure_source', 'unknown'),
            'ground_truth': example['ground_truth'],
            'honest_answer': example['honest'],
            'dishonest_answer': example['dishonest']
        }
    }

def analyze_attention_for_example(example_idx, layer_idx=-1, head_idx=0):
    """Analyze attention patterns for both scenarios of a given example."""
    
    if example_idx >= len(dataset):
        print(f"Invalid example index. Dataset has {len(dataset)} examples.")
        return None
    
    example = dataset[example_idx]
    scenarios = create_prompt_scenarios(example)
    
    print(f"\n{'='*80}")
    print(f"ATTENTION ANALYSIS - EXAMPLE {example_idx}")
    print(f"{'='*80}")
    print(f"Pressure Category: {scenarios['metadata']['pressure_category']}")
    print(f"Pressure Source: {scenarios['metadata']['pressure_source']}")
    print(f"Ground Truth: {scenarios['metadata']['ground_truth']}")
    print(f"Layer: {layer_idx} | Head: {head_idx}")
    
    results = {}
    
    # Analyze scheming scenario
    print(f"\n🔴 ANALYZING SCHEMING SCENARIO...")
    scheming_inputs, scheming_prompt = prepare_inputs_with_chat_template(
        scenarios['scheming']['system'], 
        scenarios['scheming']['user']
    )
    
    scheming_attention, scheming_tokens, scheming_ids = extract_attention_for_visualization(
        scheming_inputs, layer_idx
    )
    
    # Analyze baseline scenario
    print(f"🔵 ANALYZING BASELINE SCENARIO...")
    baseline_inputs, baseline_prompt = prepare_inputs_with_chat_template(
        scenarios['baseline']['system'], 
        scenarios['baseline']['user']
    )
    
    baseline_attention, baseline_tokens, baseline_ids = extract_attention_for_visualization(
        baseline_inputs, layer_idx
    )
    
    results = {
        'scheming': {
            'attention': scheming_attention,
            'tokens': scheming_tokens,
            'prompt': scheming_prompt,
            'system': scenarios['scheming']['system'],
            'user': scenarios['scheming']['user']
        },
        'baseline': {
            'attention': baseline_attention,
            'tokens': baseline_tokens,
            'prompt': baseline_prompt,
            'system': scenarios['baseline']['system'],
            'user': scenarios['baseline']['user']
        },
        'metadata': scenarios['metadata'],
        'layer_idx': layer_idx,
        'head_idx': head_idx
    }
    
    return results

print("Scenario analysis functions defined!")

def display_scenario_prompts(results):
    """Display the prompts for both scenarios in a formatted way."""
    
    print(f"\n{'🔴 SCHEMING SCENARIO PROMPTS':-^80}")
    print(f"\n🤖 SYSTEM PROMPT:")
    print(f"   {results['scheming']['system']}")
    print(f"\n👤 USER PROMPT:")
    print(f"   {results['scheming']['user']}")
    
    print(f"\n{'🔵 BASELINE SCENARIO PROMPTS':-^80}")
    print(f"\n🤖 SYSTEM PROMPT:")
    print(f"   {results['baseline']['system']}")
    print(f"\n👤 USER PROMPT:")
    print(f"   {results['baseline']['user']}")
    print(f"\n{'='*80}")

def create_colored_attention_visualization(results, head_idx=0):
    """Create colored token visualizations for both scenarios."""
    
    print(f"\n📊 CREATING ATTENTION VISUALIZATIONS (Head {head_idx})...")
    
    # Extract attention weights for the specified head
    scheming_head_attention = results['scheming']['attention'][head_idx]  # [seq_len, seq_len]
    baseline_head_attention = results['baseline']['attention'][head_idx]  # [seq_len, seq_len]
    
    # Average attention weights across the sequence (attention TO each token)
    scheming_avg_attention = np.mean(scheming_head_attention, axis=0)  # Average attention received by each token
    baseline_avg_attention = np.mean(baseline_head_attention, axis=0)   # Average attention received by each token
    
    # Create HTML visualizations
    scheming_html = create_colored_token_html(
        results['scheming']['tokens'],
        scheming_avg_attention,
        f"🔴 Scheming Scenario - Layer {results['layer_idx']}, Head {head_idx}",
        colormap='Reds'
    )
    
    baseline_html = create_colored_token_html(
        results['baseline']['tokens'],
        baseline_avg_attention,
        f"🔵 Baseline Scenario - Layer {results['layer_idx']}, Head {head_idx}",
        colormap='Blues'
    )
    
    # Display the visualizations
    display(HTML(scheming_html))
    display(HTML(baseline_html))
    
    return scheming_html, baseline_html

def create_attention_comparison_plots(results, head_idx=0):
    """Create interactive Plotly visualizations comparing attention patterns."""
    
    print(f"\n📈 CREATING INTERACTIVE ATTENTION PLOTS...")
    
    # Create subplot figure
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=[
            f"Scheming - All Heads (Layer {results['layer_idx']})",
            f"Baseline - All Heads (Layer {results['layer_idx']})",
            f"Scheming - Head {head_idx} Detail",
            f"Baseline - Head {head_idx} Detail"
        ],
        specs=[[{"type": "heatmap"}, {"type": "heatmap"}],
               [{"type": "scatter"}, {"type": "scatter"}]]
    )
    
    # Limit tokens for visualization
    max_tokens = 30
    
    # Scheming heatmap (all heads)
    scheming_attention = results['scheming']['attention']  # [heads, seq_len, seq_len]
    scheming_avg = np.mean(scheming_attention, axis=2)  # Average attention from each head
    
    if scheming_avg.shape[1] > max_tokens:
        step = scheming_avg.shape[1] // max_tokens
        scheming_display = scheming_avg[:, ::step]
        scheming_token_labels = [results['scheming']['tokens'][i] for i in range(0, len(results['scheming']['tokens']), step)]
    else:
        scheming_display = scheming_avg
        scheming_token_labels = results['scheming']['tokens']
    
    # Clean token labels
    scheming_clean_labels = [token.replace('▁', ' ').replace('Ġ', ' ')[:10] for token in scheming_token_labels]
    
    fig.add_trace(
        go.Heatmap(
            z=scheming_display,
            x=scheming_clean_labels,
            y=[f"H{i}" for i in range(scheming_display.shape[0])],
            colorscale='Reds',
            name="Scheming"
        ),
        row=1, col=1
    )
    
    # Baseline heatmap (all heads)
    baseline_attention = results['baseline']['attention']  # [heads, seq_len, seq_len]
    baseline_avg = np.mean(baseline_attention, axis=2)  # Average attention from each head
    
    if baseline_avg.shape[1] > max_tokens:
        step = baseline_avg.shape[1] // max_tokens
        baseline_display = baseline_avg[:, ::step]
        baseline_token_labels = [results['baseline']['tokens'][i] for i in range(0, len(results['baseline']['tokens']), step)]
    else:
        baseline_display = baseline_avg
        baseline_token_labels = results['baseline']['tokens']
    
    # Clean token labels
    baseline_clean_labels = [token.replace('▁', ' ').replace('Ġ', ' ')[:10] for token in baseline_token_labels]
    
    fig.add_trace(
        go.Heatmap(
            z=baseline_display,
            x=baseline_clean_labels,
            y=[f"H{i}" for i in range(baseline_display.shape[0])],
            colorscale='Blues',
            name="Baseline"
        ),
        row=1, col=2
    )
    
    # Detailed attention for specific head
    scheming_head_detail = np.mean(scheming_attention[head_idx], axis=0)
    baseline_head_detail = np.mean(baseline_attention[head_idx], axis=0)
    
    fig.add_trace(
        go.Scatter(
            x=list(range(len(scheming_head_detail))),
            y=scheming_head_detail,
            mode='lines+markers',
            name=f"Scheming H{head_idx}",
            line=dict(color='red')
        ),
        row=2, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=list(range(len(baseline_head_detail))),
            y=baseline_head_detail,
            mode='lines+markers',
            name=f"Baseline H{head_idx}",
            line=dict(color='blue')
        ),
        row=2, col=2
    )
    
    fig.update_layout(
        height=800,
        title_text=f"Attention Pattern Comparison - Example {results.get('example_idx', 'N/A')}",
        showlegend=False
    )
    
    fig.show()
    
    return fig

print("Visualization and comparison functions defined!")

# Create interactive widgets for analysis
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=min(len(dataset)-1, 20),  # Limit to first 20 examples for performance
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

layer_slider = widgets.IntSlider(
    value=-1,
    min=-1,
    max=31,  # Llama-3.1-8B has 32 layers (0-31), -1 for last layer
    step=1,
    description='Layer:',
    style={'description_width': 'initial'}
)

head_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=31,  # Llama-3.1-8B has 32 attention heads (0-31)
    step=1,
    description='Head:',
    style={'description_width': 'initial'}
)

# Create category filter
categories = list(set([ex.get('pressure_category', 'unknown') for ex in dataset[:21]]))
category_dropdown = widgets.Dropdown(
    options=['All'] + sorted(categories),
    value='All',
    description='Category:',
    style={'description_width': 'initial'}
)

def update_example_range(*args):
    """Update available examples based on category filter."""
    if category_dropdown.value == 'All':
        example_slider.max = min(len(dataset)-1, 20)
    else:
        filtered_indices = [i for i, ex in enumerate(dataset[:21]) 
                          if ex.get('pressure_category') == category_dropdown.value]
        if filtered_indices:
            example_slider.max = max(filtered_indices)
            example_slider.value = filtered_indices[0]

category_dropdown.observe(update_example_range, names='value')

# Display widgets
display(widgets.VBox([
    widgets.HTML('<h3>🎛️ Analysis Controls</h3>'),
    category_dropdown,
    example_slider,
    widgets.HBox([layer_slider, head_slider])
]))

print("Interactive widgets created! Use the controls above to select examples and attention parameters.")

# Run analysis on currently selected example
results = analyze_attention_for_example(
    example_slider.value, 
    layer_slider.value, 
    head_slider.value
)

if results:
    # Display prompts
    display_scenario_prompts(results)
    
    # Create colored token visualizations
    scheming_html, baseline_html = create_colored_attention_visualization(results, head_slider.value)
    
    # Create interactive comparison plots
    comparison_fig = create_attention_comparison_plots(results, head_slider.value)
    
    print("\n✅ Analysis complete! Scroll up to see the colored token visualizations.")
else:
    print("❌ Analysis failed. Please check the example index and try again.")

def analyze_attention_differences(results, head_idx=0, threshold=0.1):
    """Analyze differences in attention patterns between scenarios."""
    
    print(f"\n🔍 ANALYZING ATTENTION DIFFERENCES (Head {head_idx})...")
    
    # Get attention patterns
    scheming_attention = results['scheming']['attention'][head_idx]
    baseline_attention = results['baseline']['attention'][head_idx]
    
    # Calculate average attention to each token
    scheming_avg = np.mean(scheming_attention, axis=0)
    baseline_avg = np.mean(baseline_attention, axis=0)
    
    # Find tokens with significant attention differences
    min_len = min(len(scheming_avg), len(baseline_avg))
    attention_diff = scheming_avg[:min_len] - baseline_avg[:min_len]
    
    significant_indices = np.where(np.abs(attention_diff) > threshold)[0]
    
    differences = []
    for idx in significant_indices:
        if idx < len(results['scheming']['tokens']):
            token = results['scheming']['tokens'][idx]
            differences.append({
                'token': token.replace('▁', ' ').replace('Ġ', ' '),
                'position': idx,
                'scheming_attention': scheming_avg[idx],
                'baseline_attention': baseline_avg[idx] if idx < len(baseline_avg) else 0,
                'difference': attention_diff[idx]
            })
    
    # Sort by absolute difference
    differences.sort(key=lambda x: abs(x['difference']), reverse=True)
    
    print(f"\n📊 TOP ATTENTION DIFFERENCES (threshold: {threshold})")
    print(f"{'Rank':<4} {'Token':<20} {'Scheming':<10} {'Baseline':<10} {'Difference':<12} {'Direction':<10}")
    print("-" * 80)
    
    for i, diff in enumerate(differences[:15]):
        direction = "MORE" if diff['difference'] > 0 else "LESS"
        token_display = diff['token'][:18] + '...' if len(diff['token']) > 18 else diff['token']
        
        print(f"{i+1:<4} {token_display:<20} {diff['scheming_attention']:<10.4f} "
              f"{diff['baseline_attention']:<10.4f} {diff['difference']:<12.4f} {direction:<10}")
    
    return differences

def create_difference_visualization(differences, title="Attention Differences"):
    """Create visualization of attention differences."""
    
    if not differences:
        print("No significant differences found.")
        return
    
    # Prepare data
    tokens = [d['token'][:15] for d in differences[:20]]  # Top 20 differences
    diffs = [d['difference'] for d in differences[:20]]
    colors = ['red' if d > 0 else 'blue' for d in diffs]
    
    # Create bar plot
    fig = go.Figure(data=[
        go.Bar(
            x=tokens,
            y=diffs,
            marker_color=colors,
            hovertemplate='<b>Token:</b> %{x}<br><b>Difference:</b> %{y:.4f}<br><extra></extra>'
        )
    ])
    
    fig.update_layout(
        title=title,
        xaxis_title="Tokens",
        yaxis_title="Attention Difference (Scheming - Baseline)",
        xaxis_tickangle=-45,
        height=500
    )
    
    # Add horizontal line at y=0
    fig.add_hline(y=0, line_dash="dash", line_color="black", opacity=0.5)
    
    fig.show()
    
    return fig

print("Advanced analysis functions defined!")

# Run advanced analysis if we have results
if 'results' in locals() and results:
    print("Running advanced attention difference analysis...")
    
    # Analyze differences
    differences = analyze_attention_differences(results, head_slider.value, threshold=0.05)
    
    # Create difference visualization
    if differences:
        diff_fig = create_difference_visualization(
            differences, 
            f"Attention Differences - Example {example_slider.value}, Layer {layer_slider.value}, Head {head_slider.value}"
        )
    
    print("\n✅ Advanced analysis complete!")
else:
    print("❌ No analysis results available. Please run the main analysis first.")