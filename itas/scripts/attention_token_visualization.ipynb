{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Token-Level Attention Visualization for Scheming Dataset\n", "\n", "This notebook uses BertViz and custom visualization libraries to show attention patterns with colored tokens. Each token is colored based on its attention weight, making it easy to see which parts of the input the model focuses on.\n", "\n", "## Analysis Goals:\n", "- **Colored Token Display**: Show input text with tokens colored by attention intensity\n", "- **Interactive Attention Maps**: Hover over generated tokens to see attention patterns\n", "- **Side-by-Side Comparison**: Compare scheming vs. baseline attention visually\n", "- **Layer-wise Analysis**: Examine attention patterns across different model layers\n", "\n", "## Key Features:\n", "- Token-level attention coloring with intensity gradients\n", "- Interactive HTML visualizations\n", "- Head-wise and layer-wise attention analysis\n", "- Export capabilities for attention visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Installation and setup\n", "import sys\n", "import subprocess\n", "import os\n", "\n", "# Install required libraries\n", "required_packages = [\n", "    'bertviz',\n", "    'transformers>=4.20.0',\n", "    'torch',\n", "    'numpy',\n", "    'matplotlib',\n", "    'seaborn',\n", "    'pandas',\n", "    'ipywidgets',\n", "    'plotly'\n", "]\n", "\n", "for package in required_packages:\n", "    try:\n", "        __import__(package.split('>=')[0].split('==')[0])\n", "        print(f\"{package} already installed\")\n", "    except ImportError:\n", "        print(f\"Installing {package}...\")\n", "        subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", package])\n", "\n", "print(\"All packages installed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.colors as mcolors\n", "import seaborn as sns\n", "import pandas as pd\n", "import torch\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "from IPython.display import display, HTML\n", "import ipywidgets as widgets\n", "from ipywidgets import interact, interactive, fixed, interact_manual\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# BertViz imports\n", "from bertviz import model_view, head_view\n", "from bertviz.util import format_attention\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (15, 8)\n", "plt.rcParams['font.size'] = 10\n", "\n", "print(\"Libraries loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration and Model Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "DATASET_PATH = \"../../dataset_creation/squad_scheming_dataset.jsonl\"\n", "MODEL_NAME = \"meta-llama/Llama-3.1-8B\"\n", "\n", "# Load model and tokenizer\n", "print(f\"Loading model: {MODEL_NAME}\")\n", "tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    MODEL_NAME,\n", "    output_attentions=True,\n", "    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,\n", "    device_map=\"auto\" if torch.cuda.is_available() else None\n", ")\n", "\n", "# Add padding token if not present\n", "if tokenizer.pad_token is None:\n", "    tokenizer.pad_token = tokenizer.eos_token\n", "\n", "device = next(model.parameters()).device\n", "print(f\"Model loaded on device: {device}\")\n", "print(f\"Model has {model.config.num_hidden_layers} layers and {model.config.num_attention_heads} attention heads\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dataset Loading and Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load dataset\n", "def load_dataset(path):\n", "    data = []\n", "    with open(path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            data.append(json.loads(line.strip()))\n", "    return data\n", "\n", "dataset = load_dataset(DATASET_PATH)\n", "print(f\"Loaded {len(dataset)} examples from dataset\")\n", "\n", "# Display first example structure\n", "print(\"\\nExample structure:\")\n", "example = dataset[0]\n", "for key, value in example.items():\n", "    if isinstance(value, str) and len(value) > 100:\n", "        print(f\"{key}: {value[:100]}...\")\n", "    else:\n", "        print(f\"{key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Attention Extraction and Token Coloring Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prepare_inputs_with_chat_template(system_prompt, user_prompt, max_length=512):\n", "    \"\"\"Prepare inputs for the model using chat template.\"\"\"\n", "    # Create conversation in chat format\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "    ]\n", "    \n", "    # Apply chat template\n", "    if hasattr(tokenizer, 'apply_chat_template') and tokenizer.chat_template is not None:\n", "        formatted_prompt = tokenizer.apply_chat_template(\n", "            messages, \n", "            tokenize=False, \n", "            add_generation_prompt=True\n", "        )\n", "    else:\n", "        # Fallback to manual formatting if no chat template\n", "        formatted_prompt = f\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\"\n", "    \n", "    # Tokenize\n", "    inputs = tokenizer(\n", "        formatted_prompt,\n", "        return_tensors=\"pt\",\n", "        max_length=max_length,\n", "        truncation=True,\n", "        padding=True\n", "    )\n", "    \n", "    return inputs, formatted_prompt\n", "\n", "def extract_attention_for_visualization(inputs, layer_idx=-1, num_tokens_to_generate=5):\n", "    \"\"\"Extract attention patterns for visualization.\"\"\"\n", "    model.eval()\n", "    \n", "    with torch.no_grad():\n", "        # Move inputs to device\n", "        input_ids = inputs['input_ids'].to(device)\n", "        attention_mask = inputs['attention_mask'].to(device)\n", "        \n", "        # Get attention for the input sequence\n", "        outputs = model(\n", "            input_ids=input_ids,\n", "            attention_mask=attention_mask,\n", "            output_attentions=True\n", "        )\n", "        \n", "        # Extract attention from specified layer\n", "        attention = outputs.attentions[layer_idx]  # [batch, heads, seq_len, seq_len]\n", "        \n", "        # Convert to numpy and remove batch dimension\n", "        attention = attention[0].cpu().numpy()  # [heads, seq_len, seq_len]\n", "        \n", "        # Get tokens\n", "        tokens = tokenizer.convert_ids_to_tokens(input_ids[0])\n", "        \n", "        return attention, tokens, input_ids[0]\n", "\n", "print(\"Attention extraction functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Token Coloring Visualization Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_colored_token_html(tokens, attention_weights, title=\"Attention Visualization\", colormap='Reds'):\n", "    \"\"\"Create HTML visualization with tokens colored by attention weights.\"\"\"\n", "    \n", "    # Normalize attention weights to [0, 1]\n", "    if len(attention_weights) > 0:\n", "        min_att = np.min(attention_weights)\n", "        max_att = np.max(attention_weights)\n", "        if max_att > min_att:\n", "            normalized_weights = (attention_weights - min_att) / (max_att - min_att)\n", "        else:\n", "            normalized_weights = np.ones_like(attention_weights) * 0.5\n", "    else:\n", "        normalized_weights = np.array([])\n", "    \n", "    # Create colormap\n", "    cmap = plt.cm.get_cmap(colormap)\n", "    \n", "    # Generate HTML\n", "    html_parts = []\n", "    html_parts.append(f'<div style=\"font-family: monospace; font-size: 14px; line-height: 1.8; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #fafafa;\">')\n", "    html_parts.append(f'<h3 style=\"margin-top: 0; color: #333;\">{title}</h3>')\n", "    \n", "    for i, token in enumerate(tokens):\n", "        if i < len(normalized_weights):\n", "            # Get color from colormap\n", "            rgba = cmap(normalized_weights[i])\n", "            color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'\n", "            \n", "            # Clean token for display\n", "            display_token = token.replace('▁', ' ').replace('Ġ', ' ')\n", "            if display_token.startswith(' '):\n", "                display_token = display_token[1:]\n", "            \n", "            # Create colored span with tooltip\n", "            html_parts.append(\n", "                f'<span style=\"background-color: {color}; padding: 2px 4px; margin: 1px; '\n", "                f'border-radius: 3px; display: inline-block;\" '\n", "                f'title=\"Token: {display_token}\\nAttention: {attention_weights[i]:.4f}\\nNormalized: {normalized_weights[i]:.4f}\">{display_token}</span>'\n", "            )\n", "        else:\n", "            # Token without attention weight\n", "            display_token = token.replace('▁', ' ').replace('Ġ', ' ')\n", "            if display_token.startswith(' '):\n", "                display_token = display_token[1:]\n", "            html_parts.append(f'<span style=\"padding: 2px 4px; margin: 1px;\">{display_token}</span>')\n", "    \n", "    # Add colorbar legend\n", "    html_parts.append('<div style=\"margin-top: 15px; font-size: 12px;\">')\n", "    html_parts.append('<strong>Attention Scale:</strong> ')\n", "    \n", "    # Create mini colorbar\n", "    for i in range(10):\n", "        intensity = i / 9.0\n", "        rgba = cmap(intensity)\n", "        color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'\n", "        html_parts.append(f'<span style=\"background-color: {color}; width: 20px; height: 15px; display: inline-block; margin: 1px;\"></span>')\n", "    \n", "    if len(attention_weights) > 0:\n", "        html_parts.append(f'<br><span style=\"font-size: 10px;\">Min: {min_att:.4f} | Max: {max_att:.4f}</span>')\n", "    \n", "    html_parts.append('</div>')\n", "    html_parts.append('</div>')\n", "    \n", "    return ''.join(html_parts)\n", "\n", "def create_attention_heatmap_plotly(attention_matrix, tokens, title=\"Attention Heatmap\"):\n", "    \"\"\"Create interactive attention heatmap using Plotly.\"\"\"\n", "    \n", "    # Limit tokens for readability\n", "    max_tokens = 50\n", "    if len(tokens) > max_tokens:\n", "        step = len(tokens) // max_tokens\n", "        selected_indices = list(range(0, len(tokens), step))\n", "        display_tokens = [tokens[i] for i in selected_indices]\n", "        display_attention = attention_matrix[:, selected_indices]\n", "    else:\n", "        display_tokens = tokens\n", "        display_attention = attention_matrix\n", "    \n", "    # Clean tokens for display\n", "    clean_tokens = []\n", "    for token in display_tokens:\n", "        clean_token = token.replace('▁', ' ').replace('Ġ', ' ')\n", "        if clean_token.startswith(' '):\n", "            clean_token = clean_token[1:]\n", "        clean_tokens.append(clean_token[:15] + '...' if len(clean_token) > 15 else clean_token)\n", "    \n", "    # Create heatmap\n", "    fig = go.Figure(data=go.Heatmap(\n", "        z=display_attention,\n", "        x=clean_tokens,\n", "        y=[f\"Head {i}\" for i in range(display_attention.shape[0])],\n", "        colorscale='Reds',\n", "        hoverongaps=False,\n", "        hovertemplate='<b>Head:</b> %{y}<br><b>Token:</b> %{x}<br><b>Attention:</b> %{z:.4f}<extra></extra>'\n", "    ))\n", "    \n", "    fig.update_layout(\n", "        title=title,\n", "        xaxis_title=\"Tokens\",\n", "        yaxis_title=\"Attention Heads\",\n", "        width=1000,\n", "        height=400\n", "    )\n", "    \n", "    return fig\n", "\n", "print(\"Token coloring visualization functions defined!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}