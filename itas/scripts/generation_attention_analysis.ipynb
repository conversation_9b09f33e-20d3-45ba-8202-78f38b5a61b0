# Import required libraries
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import seaborn as sns
import pandas as pd
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from IPython.display import display, HTML
import ipywidgets as widgets
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 8)
plt.rcParams['font.size'] = 10

print("Libraries loaded successfully!")

# Configuration
DATASET_PATH = "../../dataset_creation/squad_scheming_dataset.jsonl"
MODEL_NAME = "meta-llama/Llama-3.1-8B"

# Load model and tokenizer
print(f"Loading model: {MODEL_NAME}")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    output_attentions=True,
    attn_implementation="eager",  # Required for attention extraction
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto" if torch.cuda.is_available() else None
)

# Add padding token if not present
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

device = next(model.parameters()).device
print(f"Model loaded on device: {device}")
print(f"Model has {model.config.num_hidden_layers} layers and {model.config.num_attention_heads} attention heads")

# Load dataset
def load_dataset(path):
    data = []
    with open(path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    return data

dataset = load_dataset(DATASET_PATH)
print(f"Loaded {len(dataset)} examples from dataset")

# Display first example structure
print("\nExample structure:")
example = dataset[0]
for key, value in example.items():
    if isinstance(value, str) and len(value) > 100:
        print(f"{key}: {value[:100]}...")
    else:
        print(f"{key}: {value}")

def prepare_inputs_with_chat_template(system_prompt, user_prompt, max_length=512):
    """Prepare inputs for the model using chat template."""
    # Create conversation in chat format
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]
    
    # Apply chat template
    if hasattr(tokenizer, 'apply_chat_template') and tokenizer.chat_template is not None:
        formatted_prompt = tokenizer.apply_chat_template(
            messages, 
            tokenize=False, 
            add_generation_prompt=True
        )
    else:
        # Fallback to manual formatting if no chat template
        formatted_prompt = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
    
    # Tokenize
    inputs = tokenizer(
        formatted_prompt,
        return_tensors="pt",
        max_length=max_length,
        truncation=True,
        padding=True
    )
    
    return inputs, formatted_prompt

def extract_generation_attention(inputs, layer_idx=-1, max_new_tokens=10):
    """Extract attention patterns during text generation."""
    model.eval()
    
    generation_data = {
        'input_tokens': [],
        'generated_tokens': [],
        'generation_attentions': [],
        'input_length': 0,
        'full_sequence_tokens': []
    }
    
    with torch.no_grad():
        # Move inputs to device
        input_ids = inputs['input_ids'].to(device)
        attention_mask = inputs['attention_mask'].to(device)
        
        # Store input information
        input_tokens = tokenizer.convert_ids_to_tokens(input_ids[0])
        generation_data['input_tokens'] = input_tokens
        generation_data['input_length'] = len(input_tokens)
        
        # Generate tokens one by one to capture attention at each step
        current_ids = input_ids.clone()
        current_mask = attention_mask.clone()
        
        for step in range(max_new_tokens):
            # Get model output with attention
            outputs = model(
                input_ids=current_ids,
                attention_mask=current_mask,
                output_attentions=True,
                use_cache=False  # Disable cache for consistent attention extraction
            )
            
            # Get next token
            logits = outputs.logits[0, -1, :]  # Last token logits
            next_token_id = torch.argmax(logits, dim=-1).unsqueeze(0).unsqueeze(0)
            next_token = tokenizer.convert_ids_to_tokens([next_token_id.item()])[0]
            
            # Store generated token
            generation_data['generated_tokens'].append(next_token)
            
            # Extract attention from the last position (newly generated token)
            # This shows what the generated token is attending to
            step_attention = outputs.attentions[layer_idx][0]  # [heads, seq_len, seq_len]
            last_token_attention = step_attention[:, -1, :]  # [heads, seq_len] - attention FROM last token TO all tokens
            
            generation_data['generation_attentions'].append(last_token_attention.cpu().numpy())
            
            # Store full sequence tokens at this step
            full_tokens = tokenizer.convert_ids_to_tokens(current_ids[0]) + [next_token]
            generation_data['full_sequence_tokens'].append(full_tokens.copy())
            
            # Update for next iteration
            current_ids = torch.cat([current_ids, next_token_id], dim=1)
            current_mask = torch.cat([current_mask, torch.ones(1, 1, device=device)], dim=1)
            
            # Stop if we hit EOS token
            if next_token_id.item() == tokenizer.eos_token_id:
                break
        
        return generation_data

print("Generation attention extraction functions defined!")

def analyze_generation_attention(generation_data, head_idx=0):
    """Analyze attention patterns from generated tokens to input tokens."""
    
    if not generation_data['generated_tokens']:
        print("No generated tokens found.")
        return None
    
    input_length = generation_data['input_length']
    input_tokens = generation_data['input_tokens']
    generated_tokens = generation_data['generated_tokens']
    
    print(f"\n🎯 GENERATION ATTENTION ANALYSIS (Head {head_idx})")
    print(f"Input length: {input_length} tokens")
    print(f"Generated: {len(generated_tokens)} tokens")
    
    # Convert generated tokens to readable text
    generated_text = tokenizer.convert_tokens_to_string(generated_tokens)
    print(f"Generated text: \"{generated_text}\"")
    
    # Analyze each generated token's attention to input
    token_analyses = []
    
    for step, (gen_token, attention_weights) in enumerate(zip(generated_tokens, generation_data['generation_attentions'])):
        # Get attention from this generated token to input tokens
        head_attention = attention_weights[head_idx]  # [seq_len]
        input_attention = head_attention[:input_length]  # Only attention to input tokens
        
        # Find top attended input tokens
        top_indices = np.argsort(input_attention)[-5:][::-1]  # Top 5
        
        analysis = {
            'step': step,
            'generated_token': gen_token,
            'generated_text': tokenizer.convert_tokens_to_string([gen_token]),
            'input_attention': input_attention,
            'top_attended_tokens': []
        }
        
        for idx in top_indices:
            if idx < len(input_tokens):
                analysis['top_attended_tokens'].append({
                    'token': input_tokens[idx],
                    'text': tokenizer.convert_tokens_to_string([input_tokens[idx]]),
                    'attention': input_attention[idx],
                    'position': idx
                })
        
        token_analyses.append(analysis)
        
        # Print analysis for this token
        print(f"\n📍 Step {step + 1}: Generated '{analysis['generated_text']}'")
        print(f"   Top attended input tokens:")
        for i, token_info in enumerate(analysis['top_attended_tokens']):
            print(f"   {i+1}. '{token_info['text']}' (pos {token_info['position']}) - {token_info['attention']:.4f}")
    
    return token_analyses

print("Analysis functions defined!")

def create_generation_attention_heatmap(generation_data, head_idx=0, title="Generation Attention Heatmap"):
    """Create heatmap showing attention from generated tokens to input tokens."""
    
    if not generation_data['generated_tokens']:
        print("No generation data available.")
        return None
    
    input_length = generation_data['input_length']
    input_tokens = generation_data['input_tokens']
    generated_tokens = generation_data['generated_tokens']
    
    # Build attention matrix: [generated_tokens, input_tokens]
    attention_matrix = []
    
    for attention_weights in generation_data['generation_attentions']:
        head_attention = attention_weights[head_idx]  # [seq_len]
        input_attention = head_attention[:input_length]  # Only attention to input tokens
        attention_matrix.append(input_attention)
    
    attention_matrix = np.array(attention_matrix)  # [num_generated, input_length]
    
    # Clean tokens for display (NO TRUNCATION)
    clean_input_tokens = []
    for token in input_tokens:
        clean_token = token.replace('▁', ' ').replace('Ġ', ' ')
        if clean_token.startswith(' '):
            clean_token = clean_token[1:]
        clean_input_tokens.append(clean_token)  # Full token, no truncation
    
    clean_generated_tokens = []
    for token in generated_tokens:
        clean_token = token.replace('▁', ' ').replace('Ġ', ' ')
        if clean_token.startswith(' '):
            clean_token = clean_token[1:]
        clean_generated_tokens.append(clean_token)  # Full token, no truncation
    
    # Create interactive heatmap
    fig = go.Figure(data=go.Heatmap(
        z=attention_matrix,
        x=clean_input_tokens,
        y=[f"Gen{i+1}: {token}" for i, token in enumerate(clean_generated_tokens)],
        colorscale='Reds',
        hoverongaps=False,
        hovertemplate='<b>Generated:</b> %{y}<br><b>Input Token:</b> %{x}<br><b>Attention:</b> %{z:.4f}<extra></extra>'
    ))
    
    fig.update_layout(
        title=f"{title} (Head {head_idx})",
        xaxis_title="Input Tokens (Full Text)",
        yaxis_title="Generated Tokens",
        width=max(1200, len(clean_input_tokens) * 15),  # Dynamic width based on input length
        height=400 + len(generated_tokens) * 30,
        xaxis_tickangle=-45
    )
    
    return fig

def create_colored_input_visualization(generation_data, target_gen_step=0, head_idx=0):
    """Create colored visualization of input tokens based on attention from a specific generated token."""
    
    if target_gen_step >= len(generation_data['generated_tokens']):
        print(f"Invalid generation step. Only {len(generation_data['generated_tokens'])} tokens generated.")
        return None
    
    input_tokens = generation_data['input_tokens']
    generated_token = generation_data['generated_tokens'][target_gen_step]
    attention_weights = generation_data['generation_attentions'][target_gen_step]
    
    # Get attention from the generated token to input tokens
    head_attention = attention_weights[head_idx]
    input_attention = head_attention[:len(input_tokens)]
    
    # Normalize attention weights to [0, 1]
    if len(input_attention) > 0:
        min_att = np.min(input_attention)
        max_att = np.max(input_attention)
        if max_att > min_att:
            normalized_weights = (input_attention - min_att) / (max_att - min_att)
        else:
            normalized_weights = np.ones_like(input_attention) * 0.5
    else:
        normalized_weights = np.array([])
    
    # Create colormap
    cmap = plt.cm.get_cmap('Reds')
    
    # Generate HTML
    generated_text = tokenizer.convert_tokens_to_string([generated_token])
    html_parts = []
    html_parts.append(f'<div style="font-family: monospace; font-size: 14px; line-height: 1.8; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #fafafa; max-width: 100%; overflow-wrap: break-word;">')
    html_parts.append(f'<h3 style="margin-top: 0; color: #333;">Attention from Generated Token \"{generated_text}\" to Input (Head {head_idx})</h3>')
    html_parts.append(f'<div style="margin-bottom: 15px; font-weight: bold;">Full Input Text with Attention Coloring:</div>')
    
    for i, token in enumerate(input_tokens):
        if i < len(normalized_weights):
            # Get color from colormap
            rgba = cmap(normalized_weights[i])
            color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
            
            # Clean token for display (NO TRUNCATION)
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]
            
            # Create colored span with tooltip
            html_parts.append(
                f'<span style="background-color: {color}; padding: 2px 4px; margin: 1px; '
                f'border-radius: 3px; display: inline-block; word-break: break-all;" '
                f'title="Token: {display_token}\\nAttention: {input_attention[i]:.4f}\\nNormalized: {normalized_weights[i]:.4f}\\nPosition: {i}">{display_token}</span>'
            )
        else:
            # Token without attention weight
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]
            html_parts.append(f'<span style="padding: 2px 4px; margin: 1px; word-break: break-all;">{display_token}</span>')
    
    # Add colorbar legend
    html_parts.append('<div style="margin-top: 15px; font-size: 12px;">')
    html_parts.append('<strong>Attention Scale:</strong> ')
    
    # Create mini colorbar
    for i in range(10):
        intensity = i / 9.0
        rgba = cmap(intensity)
        color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
        html_parts.append(f'<span style="background-color: {color}; width: 20px; height: 15px; display: inline-block; margin: 1px;"></span>')
    
    if len(input_attention) > 0:
        html_parts.append(f'<br><span style="font-size: 10px;">Min: {min_att:.4f} | Max: {max_att:.4f}</span>')
    
    html_parts.append('</div>')
    html_parts.append('</div>')
    
    return ''.join(html_parts)

def create_full_scenario_visualization(results, target_gen_step=0):
    """Create comprehensive visualization showing both scenarios side by side."""
    
    print(f"\n{'='*100}")
    print(f"COMPREHENSIVE ATTENTION VISUALIZATION - GENERATION STEP {target_gen_step + 1}")
    print(f"{'='*100}")
    
    # Display full prompts without truncation
    print(f"\n🔴 SCHEMING SCENARIO FULL PROMPTS:")
    print(f"\n🤖 SYSTEM PROMPT:")
    print(f"{results['scheming']['system']}")
    print(f"\n👤 USER PROMPT:")
    print(f"{results['scheming']['user']}")
    
    print(f"\n🔵 BASELINE SCENARIO FULL PROMPTS:")
    print(f"\n🤖 SYSTEM PROMPT:")
    print(f"{results['baseline']['system']}")
    print(f"\n👤 USER PROMPT:")
    print(f"{results['baseline']['user']}")
    
    # Show generated responses
    scheming_tokens = results['scheming']['generation_data']['generated_tokens']
    baseline_tokens = results['baseline']['generation_data']['generated_tokens']
    
    if scheming_tokens:
        scheming_response = tokenizer.convert_tokens_to_string(scheming_tokens)
        print(f"\n🔴 SCHEMING RESPONSE: \"{scheming_response}\"")
    
    if baseline_tokens:
        baseline_response = tokenizer.convert_tokens_to_string(baseline_tokens)
        print(f"🔵 BASELINE RESPONSE: \"{baseline_response}\"")
    
    # Create colored visualizations
    if target_gen_step < len(scheming_tokens):
        scheming_html = create_colored_input_visualization(
            results['scheming']['generation_data'], 
            target_gen_step, 
            results['head_idx']
        )
        print(f"\n📊 SCHEMING SCENARIO - COLORED INPUT VISUALIZATION:")
        display(HTML(scheming_html))
    
    if target_gen_step < len(baseline_tokens):
        baseline_html = create_colored_input_visualization(
            results['baseline']['generation_data'], 
            target_gen_step, 
            results['head_idx']
        )
        print(f"\n📊 BASELINE SCENARIO - COLORED INPUT VISUALIZATION:")
        display(HTML(baseline_html))
    
    # Create heatmaps
    print(f"\n📈 INTERACTIVE ATTENTION HEATMAPS:")
    
    scheming_heatmap = create_generation_attention_heatmap(
        results['scheming']['generation_data'],
        results['head_idx'],
        "🔴 Scheming Scenario - Generation Attention"
    )
    if scheming_heatmap:
        scheming_heatmap.show()
    
    baseline_heatmap = create_generation_attention_heatmap(
        results['baseline']['generation_data'],
        results['head_idx'],
        "🔵 Baseline Scenario - Generation Attention"
    )
    if baseline_heatmap:
        baseline_heatmap.show()

print("Visualization functions defined!")

def create_prompt_scenarios(example):
    """Create scheming and baseline prompt scenarios from dataset example."""
    
    # Scenario A: Scheming (use pressure-inducing system prompt)
    scheming_system = example['system_prompt']
    scheming_user = example['user_prompt']
    
    # Scenario B: Baseline (neutral system prompt with original context)
    baseline_system = "You are a helpful assistant."
    baseline_user = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
    
    return {
        'scheming': {
            'system': scheming_system,
            'user': scheming_user
        },
        'baseline': {
            'system': baseline_system,
            'user': baseline_user
        },
        'metadata': {
            'pressure_category': example.get('pressure_category', 'unknown'),
            'pressure_source': example.get('pressure_source', 'unknown'),
            'ground_truth': example['ground_truth'],
            'honest_answer': example['honest'],
            'dishonest_answer': example['dishonest']
        }
    }

def analyze_example_generation_attention(example_idx, layer_idx=-1, head_idx=0, max_tokens=10):
    """Analyze generation attention for both scenarios of a given example."""
    
    if example_idx >= len(dataset):
        print(f"Invalid example index. Dataset has {len(dataset)} examples.")
        return None
    
    example = dataset[example_idx]
    scenarios = create_prompt_scenarios(example)
    
    print(f"\n{'='*80}")
    print(f"GENERATION ATTENTION ANALYSIS - EXAMPLE {example_idx}")
    print(f"{'='*80}")
    print(f"Pressure Category: {scenarios['metadata']['pressure_category']}")
    print(f"Pressure Source: {scenarios['metadata']['pressure_source']}")
    print(f"Ground Truth: {scenarios['metadata']['ground_truth']}")
    print(f"Expected Honest: {scenarios['metadata']['honest_answer']}")
    print(f"Expected Dishonest: {scenarios['metadata']['dishonest_answer']}")
    print(f"Layer: {layer_idx} | Head: {head_idx}")
    
    results = {}
    
    # Analyze scheming scenario
    print(f"\n🔴 ANALYZING SCHEMING SCENARIO...")
    print(f"System: {scenarios['scheming']['system']}")
    print(f"User: {scenarios['scheming']['user']}")
    
    scheming_inputs, scheming_prompt = prepare_inputs_with_chat_template(
        scenarios['scheming']['system'], 
        scenarios['scheming']['user']
    )
    
    scheming_generation_data = extract_generation_attention(
        scheming_inputs, layer_idx, max_tokens
    )
    
    scheming_analysis = analyze_generation_attention(scheming_generation_data, head_idx)
    
    # Analyze baseline scenario
    print(f"\n🔵 ANALYZING BASELINE SCENARIO...")
    print(f"System: {scenarios['baseline']['system']}")
    print(f"User: {scenarios['baseline']['user']}")
    
    baseline_inputs, baseline_prompt = prepare_inputs_with_chat_template(
        scenarios['baseline']['system'], 
        scenarios['baseline']['user']
    )
    
    baseline_generation_data = extract_generation_attention(
        baseline_inputs, layer_idx, max_tokens
    )
    
    baseline_analysis = analyze_generation_attention(baseline_generation_data, head_idx)
    
    results = {
        'scheming': {
            'generation_data': scheming_generation_data,
            'analysis': scheming_analysis,
            'system': scenarios['scheming']['system'],
            'user': scenarios['scheming']['user']
        },
        'baseline': {
            'generation_data': baseline_generation_data,
            'analysis': baseline_analysis,
            'system': scenarios['baseline']['system'],
            'user': scenarios['baseline']['user']
        },
        'metadata': scenarios['metadata'],
        'layer_idx': layer_idx,
        'head_idx': head_idx,
        'example_idx': example_idx
    }
    
    return results

print("Scenario analysis functions defined!")

# Create interactive widgets for analysis
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=min(len(dataset)-1, 10),  # Limit to first 10 examples for performance
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

layer_slider = widgets.IntSlider(
    value=-1,
    min=-1,
    max=31,  # Llama-3.1-8B has 32 layers (0-31), -1 for last layer
    step=1,
    description='Layer:',
    style={'description_width': 'initial'}
)

head_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=31,  # Llama-3.1-8B has 32 attention heads (0-31)
    step=1,
    description='Head:',
    style={'description_width': 'initial'}
)

max_tokens_slider = widgets.IntSlider(
    value=5,
    min=1,
    max=20,
    step=1,
    description='Max Tokens:',
    style={'description_width': 'initial'}
)

# Display widgets
display(widgets.VBox([
    widgets.HTML('<h3>🎛️ Generation Attention Analysis Controls</h3>'),
    example_slider,
    widgets.HBox([layer_slider, head_slider]),
    max_tokens_slider
]))

print("Interactive widgets created! Use the controls above to select examples and parameters.")

# Run analysis on currently selected example
results = analyze_example_generation_attention(
    example_slider.value, 
    layer_slider.value, 
    head_slider.value,
    max_tokens_slider.value
)

if results:
    print("\n✅ Generation attention analysis complete!")
    print("\nKey findings:")
    
    # Compare first generated tokens
    if (results['scheming']['generation_data']['generated_tokens'] and 
        results['baseline']['generation_data']['generated_tokens']):
        
        scheming_first = results['scheming']['generation_data']['generated_tokens'][0]
        baseline_first = results['baseline']['generation_data']['generated_tokens'][0]
        
        scheming_text = tokenizer.convert_tokens_to_string([scheming_first])
        baseline_text = tokenizer.convert_tokens_to_string([baseline_first])
        
        print(f"\n🔴 Scheming first token: '{scheming_text}'")
        print(f"🔵 Baseline first token: '{baseline_text}'")
        
        if scheming_first != baseline_first:
            print(f"\n⚠️  Different first tokens generated! This suggests the pressure is affecting the model's response.")
        else:
            print(f"\n✓ Same first token generated in both scenarios.")
    
    print("\n📊 Scroll up to see detailed attention analysis for each generated token.")
else:
    print("❌ Analysis failed. Please check the example index and try again.")

# Create comprehensive visualization if we have results
if 'results' in locals() and results:
    print("Creating comprehensive attention visualizations...")
    
    # Show visualization for the first generated token (most important)
    create_full_scenario_visualization(results, target_gen_step=0)
    
    print("\n" + "="*100)
    print("VISUALIZATION COMPLETE!")
    print("="*100)
    print("\n🎯 What you're seeing:")
    print("1. 📝 Full prompts and responses (no truncation)")
    print("2. 🎨 Colored input text showing attention intensity")
    print("3. 📊 Interactive heatmaps showing attention patterns")
    print("4. 🔍 Hover over tokens and heatmap cells for detailed values")
    
    print("\n💡 Key insights to look for:")
    print("- Darker red colors = Higher attention")
    print("- Compare which words get attention in scheming vs baseline")
    print("- Look for attention to authority words vs factual content")
    print("- Check if pressure words drive the model's response")
    
else:
    print("❌ No analysis results available. Please run the generation analysis first.")

# Widget to select which generated token to analyze
if 'results' in locals() and results:
    max_tokens_generated = max(
        len(results['scheming']['generation_data']['generated_tokens']),
        len(results['baseline']['generation_data']['generated_tokens'])
    )
    
    if max_tokens_generated > 0:
        token_step_slider = widgets.IntSlider(
            value=0,
            min=0,
            max=max_tokens_generated - 1,
            step=1,
            description='Token Step:',
            style={'description_width': 'initial'}
        )
        
        display(widgets.VBox([
            widgets.HTML('<h3>🔍 Select Generated Token to Analyze</h3>'),
            token_step_slider,
            widgets.HTML('<p>Use the slider to see attention patterns for different generated tokens.</p>')
        ]))
        
        print("Use the slider above to explore attention for different generated tokens.")
    else:
        print("No tokens were generated to analyze.")
else:
    print("Run the analysis first to enable token-by-token exploration.")

# Run visualization for selected token step
if 'results' in locals() and results and 'token_step_slider' in locals():
    create_full_scenario_visualization(results, target_gen_step=token_step_slider.value)
else:
    print("Please run the analysis and use the token step slider above.")

def calculate_average_generation_attention(generation_data, head_idx=0):
    """Calculate average attention across all generated tokens."""
    
    if not generation_data['generated_tokens']:
        print("No generation data available.")
        return None
    
    input_length = generation_data['input_length']
    input_tokens = generation_data['input_tokens']
    generated_tokens = generation_data['generated_tokens']
    
    # Collect attention weights for all generated tokens
    all_attention_weights = []
    
    for attention_weights in generation_data['generation_attentions']:
        head_attention = attention_weights[head_idx]  # [seq_len]
        input_attention = head_attention[:input_length]  # Only attention to input tokens
        all_attention_weights.append(input_attention)
    
    # Calculate average attention across all generated tokens
    if all_attention_weights:
        average_attention = np.mean(all_attention_weights, axis=0)  # [input_length]
        
        # Find top attended input tokens
        top_indices = np.argsort(average_attention)[-10:][::-1]  # Top 10
        
        analysis = {
            'average_attention': average_attention,
            'input_tokens': input_tokens,
            'generated_tokens': generated_tokens,
            'top_attended_tokens': []
        }
        
        for idx in top_indices:
            if idx < len(input_tokens):
                analysis['top_attended_tokens'].append({
                    'token': input_tokens[idx],
                    'text': tokenizer.convert_tokens_to_string([input_tokens[idx]]),
                    'attention': average_attention[idx],
                    'position': idx
                })
        
        return analysis
    
    return None

def create_average_attention_visualization(generation_data, head_idx=0, title="Average Attention Across All Generated Tokens"):
    """Create colored visualization showing average attention across all generated tokens."""
    
    analysis = calculate_average_generation_attention(generation_data, head_idx)
    
    if not analysis:
        print("No analysis data available.")
        return None
    
    input_tokens = analysis['input_tokens']
    average_attention = analysis['average_attention']
    generated_tokens = analysis['generated_tokens']
    
    # Normalize attention weights to [0, 1]
    if len(average_attention) > 0:
        min_att = np.min(average_attention)
        max_att = np.max(average_attention)
        if max_att > min_att:
            normalized_weights = (average_attention - min_att) / (max_att - min_att)
        else:
            normalized_weights = np.ones_like(average_attention) * 0.5
    else:
        normalized_weights = np.array([])
    
    # Create colormap
    cmap = plt.cm.get_cmap('Purples')  # Use purple to distinguish from single-token analysis
    
    # Generate HTML
    generated_text = tokenizer.convert_tokens_to_string(generated_tokens)
    html_parts = []
    html_parts.append(f'<div style="font-family: monospace; font-size: 14px; line-height: 1.8; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #fafafa; max-width: 100%; overflow-wrap: break-word;">')
    html_parts.append(f'<h3 style="margin-top: 0; color: #333;">{title} (Head {head_idx})</h3>')
    html_parts.append(f'<div style="margin-bottom: 10px; font-weight: bold;">Generated Response: \"{generated_text}\"</div>')
    html_parts.append(f'<div style="margin-bottom: 15px; font-weight: bold;">Average Attention Across {len(generated_tokens)} Generated Tokens:</div>')
    
    for i, token in enumerate(input_tokens):
        if i < len(normalized_weights):
            # Get color from colormap
            rgba = cmap(normalized_weights[i])
            color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
            
            # Clean token for display
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]
            
            # Create colored span with tooltip
            html_parts.append(
                f'<span style="background-color: {color}; padding: 2px 4px; margin: 1px; '
                f'border-radius: 3px; display: inline-block; word-break: break-all;" '
                f'title="Token: {display_token}\\nAverage Attention: {average_attention[i]:.4f}\\nNormalized: {normalized_weights[i]:.4f}\\nPosition: {i}">{display_token}</span>'
            )
        else:
            # Token without attention weight
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]
            html_parts.append(f'<span style="padding: 2px 4px; margin: 1px; word-break: break-all;">{display_token}</span>')
    
    # Add colorbar legend
    html_parts.append('<div style="margin-top: 15px; font-size: 12px;">')
    html_parts.append('<strong>Average Attention Scale:</strong> ')
    
    # Create mini colorbar
    for i in range(10):
        intensity = i / 9.0
        rgba = cmap(intensity)
        color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
        html_parts.append(f'<span style="background-color: {color}; width: 20px; height: 15px; display: inline-block; margin: 1px;"></span>')
    
    if len(average_attention) > 0:
        html_parts.append(f'<br><span style="font-size: 10px;">Min: {min_att:.4f} | Max: {max_att:.4f}</span>')
    
    html_parts.append('</div>')
    html_parts.append('</div>')
    
    return ''.join(html_parts), analysis

def create_average_attention_comparison_plot(scheming_analysis, baseline_analysis, head_idx=0):
    """Create comparison plot of average attention patterns between scenarios."""
    
    if not scheming_analysis or not baseline_analysis:
        print("Missing analysis data for comparison.")
        return None
    
    # Get top tokens from both scenarios
    scheming_top = scheming_analysis['top_attended_tokens'][:15]
    baseline_top = baseline_analysis['top_attended_tokens'][:15]
    
    # Create comparison plot
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=["🔴 Scheming - Top Average Attention", "🔵 Baseline - Top Average Attention"],
        specs=[[{"type": "bar"}, {"type": "bar"}]]
    )
    
    # Scheming scenario
    scheming_tokens = [item['text'][:15] for item in scheming_top]
    scheming_attentions = [item['attention'] for item in scheming_top]
    
    fig.add_trace(
        go.Bar(
            x=scheming_tokens,
            y=scheming_attentions,
            name="Scheming",
            marker_color='red',
            hovertemplate='<b>Token:</b> %{x}<br><b>Avg Attention:</b> %{y:.4f}<extra></extra>'
        ),
        row=1, col=1
    )
    
    # Baseline scenario
    baseline_tokens = [item['text'][:15] for item in baseline_top]
    baseline_attentions = [item['attention'] for item in baseline_top]
    
    fig.add_trace(
        go.Bar(
            x=baseline_tokens,
            y=baseline_attentions,
            name="Baseline",
            marker_color='blue',
            hovertemplate='<b>Token:</b> %{x}<br><b>Avg Attention:</b> %{y:.4f}<extra></extra>'
        ),
        row=1, col=2
    )
    
    fig.update_layout(
        title=f"Average Attention Comparison Across All Generated Tokens (Head {head_idx})",
        showlegend=False,
        height=500
    )
    
    fig.update_xaxes(tickangle=-45)
    fig.update_yaxes(title_text="Average Attention")
    
    return fig

print("Average attention analysis functions defined!")

# Analyze average attention across all generated tokens
if 'results' in locals() and results:
    print("\n" + "="*100)
    print("AVERAGE ATTENTION ANALYSIS ACROSS ALL GENERATED TOKENS")
    print("="*100)
    
    # Calculate average attention for both scenarios
    scheming_avg_analysis = calculate_average_generation_attention(
        results['scheming']['generation_data'], 
        results['head_idx']
    )
    
    baseline_avg_analysis = calculate_average_generation_attention(
        results['baseline']['generation_data'], 
        results['head_idx']
    )
    
    if scheming_avg_analysis and baseline_avg_analysis:
        # Print top attended tokens for each scenario
        print(f"\n🔴 SCHEMING SCENARIO - TOP AVERAGE ATTENTION:")
        scheming_response = tokenizer.convert_tokens_to_string(scheming_avg_analysis['generated_tokens'])
        print(f"Generated Response: \"{scheming_response}\"")
        print(f"Top attended input tokens (averaged across all {len(scheming_avg_analysis['generated_tokens'])} generated tokens):")
        
        for i, token_info in enumerate(scheming_avg_analysis['top_attended_tokens'][:10]):
            print(f"   {i+1:2d}. '{token_info['text']}' (pos {token_info['position']}) - {token_info['attention']:.4f}")
        
        print(f"\n🔵 BASELINE SCENARIO - TOP AVERAGE ATTENTION:")
        baseline_response = tokenizer.convert_tokens_to_string(baseline_avg_analysis['generated_tokens'])
        print(f"Generated Response: \"{baseline_response}\"")
        print(f"Top attended input tokens (averaged across all {len(baseline_avg_analysis['generated_tokens'])} generated tokens):")
        
        for i, token_info in enumerate(baseline_avg_analysis['top_attended_tokens'][:10]):
            print(f"   {i+1:2d}. '{token_info['text']}' (pos {token_info['position']}) - {token_info['attention']:.4f}")
        
        # Create colored visualizations
        print(f"\n📊 AVERAGE ATTENTION VISUALIZATIONS:")
        
        scheming_avg_html, _ = create_average_attention_visualization(
            results['scheming']['generation_data'],
            results['head_idx'],
            "🔴 Scheming - Average Attention Across All Generated Tokens"
        )
        
        if scheming_avg_html:
            display(HTML(scheming_avg_html))
        
        baseline_avg_html, _ = create_average_attention_visualization(
            results['baseline']['generation_data'],
            results['head_idx'],
            "🔵 Baseline - Average Attention Across All Generated Tokens"
        )
        
        if baseline_avg_html:
            display(HTML(baseline_avg_html))
        
        # Create comparison plot
        print(f"\n📈 AVERAGE ATTENTION COMPARISON PLOT:")
        comparison_fig = create_average_attention_comparison_plot(
            scheming_avg_analysis, 
            baseline_avg_analysis, 
            results['head_idx']
        )
        
        if comparison_fig:
            comparison_fig.show()
        
        print(f"\n✅ Average attention analysis complete!")
        print(f"\n💡 Key insights:")
        print(f"- Purple coloring shows average attention across ALL generated tokens")
        print(f"- Compare which input words get consistent attention throughout the response")
        print(f"- Look for systematic differences between scheming and baseline scenarios")
        print(f"- Darker purple = higher average attention across the entire response")
        
    else:
        print("❌ Could not calculate average attention. Check if tokens were generated.")
        
else:
    print("❌ No analysis results available. Please run the generation analysis first.")