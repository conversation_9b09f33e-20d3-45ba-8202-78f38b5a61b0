{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Generation Attention Analysis: What Does the Model Look At When Generating Responses?\n", "\n", "This notebook analyzes attention patterns **during text generation** to understand what parts of the input the model focuses on when generating specific output tokens like \"Yes\", \"No\", or factual answers.\n", "\n", "## Key Features:\n", "- **🎨 Colored token visualization** - See input text with tokens colored by attention intensity\n", "- **📊 Interactive heatmaps** - Attention matrices showing generation patterns\n", "- **🔍 Token-by-token analysis** - What each generated token attends to\n", "- **📈 Average attention analysis** - Overall attention patterns across entire responses\n", "- **🔄 Scenario comparison** - Scheming vs. baseline attention differences\n", "- **📝 Full text display** - No truncation of prompts or responses\n", "\n", "## Research Questions Answered:\n", "- When the model generates \"Yes\", what input tokens is it paying attention to?\n", "- How does pressure change attention patterns during generation?\n", "- Which input words drive dishonest vs. honest responses?\n", "- What are the mechanistic differences in attention between scenarios?"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.colors as mcolors\n", "import seaborn as sns\n", "import pandas as pd\n", "import torch\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "from IPython.display import display, HTML\n", "import ipywidgets as widgets\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (15, 8)\n", "plt.rcParams['font.size'] = 10\n", "\n", "print(\"Libraries loaded successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "DATASET_PATH = \"../../dataset_creation/squad_scheming_dataset.jsonl\"\n", "MODEL_NAME = \"meta-llama/Llama-3.1-8B\"\n", "\n", "# Load model and tokenizer\n", "print(f\"Loading model: {MODEL_NAME}\")\n", "tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    MODEL_NAME,\n", "    output_attentions=True,\n", "    attn_implementation=\"eager\",  # Required for attention extraction\n", "    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,\n", "    device_map=\"auto\" if torch.cuda.is_available() else None\n", ")\n", "\n", "# Add padding token if not present\n", "if tokenizer.pad_token is None:\n", "    tokenizer.pad_token = tokenizer.eos_token\n", "\n", "device = next(model.parameters()).device\n", "print(f\"Model loaded on device: {device}\")\n", "print(f\"Model has {model.config.num_hidden_layers} layers and {model.config.num_attention_heads} attention heads\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load dataset\n", "def load_dataset(path):\n", "    data = []\n", "    with open(path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            data.append(json.loads(line.strip()))\n", "    return data\n", "\n", "dataset = load_dataset(DATASET_PATH)\n", "print(f\"Loaded {len(dataset)} examples from dataset\")\n", "\n", "# Display first example structure\n", "print(\"\\nExample structure:\")\n", "example = dataset[0]\n", "for key, value in example.items():\n", "    if isinstance(value, str) and len(value) > 100:\n", "        print(f\"{key}: {value[:100]}...\")\n", "    else:\n", "        print(f\"{key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def apply_chat_template(system_prompt, user_prompt):\n", "    \"\"\"Apply chat template to prompts.\"\"\"\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "    ]\n", "    return tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)\n", "\n", "def generate_with_attention(prompt, max_new_tokens=5):\n", "    \"\"\"Generate text while capturing attention weights for each generated token.\"\"\"\n", "    \n", "    # Tokenize input\n", "    inputs = tokenizer(prompt, return_tensors=\"pt\", padding=True, truncation=True)\n", "    input_ids = inputs[\"input_ids\"].to(device)\n", "    attention_mask = inputs[\"attention_mask\"].to(device)\n", "    \n", "    input_length = input_ids.shape[1]\n", "    input_tokens = tokenizer.convert_ids_to_tokens(input_ids[0])\n", "    \n", "    # Store generation data\n", "    generation_data = {\n", "        'input_tokens': input_tokens,\n", "        'input_length': input_length,\n", "        'generated_tokens': [],\n", "        'generation_attentions': [],  # Attention weights for each generated token\n", "        'full_prompt': prompt\n", "    }\n", "    \n", "    # Generate tokens one by one\n", "    current_ids = input_ids.clone()\n", "    \n", "    for step in range(max_new_tokens):\n", "        # Forward pass with attention\n", "        with torch.no_grad():\n", "            outputs = model(\n", "                current_ids,\n", "                attention_mask=attention_mask,\n", "                output_attentions=True,\n", "                use_cache=False\n", "            )\n", "        \n", "        # Get next token\n", "        logits = outputs.logits[0, -1, :]  # Last token logits\n", "        next_token_id = torch.argmax(logits, dim=-1).unsqueeze(0).unsqueeze(0)\n", "        \n", "        # Convert to token\n", "        next_token = tokenizer.convert_ids_to_tokens([next_token_id.item()])[0]\n", "        generation_data['generated_tokens'].append(next_token)\n", "        \n", "        # Extract attention weights from the last layer for the newly generated token\n", "        # Shape: [batch_size, num_heads, seq_len, seq_len]\n", "        last_layer_attention = outputs.attentions[-1][0]  # [num_heads, seq_len, seq_len]\n", "        \n", "        # Get attention from the last position (newly generated token) to all previous positions\n", "        generation_attention = last_layer_attention[:, -1, :]  # [num_heads, seq_len]\n", "        generation_data['generation_attentions'].append(generation_attention.cpu().numpy())\n", "        \n", "        # Update current_ids for next iteration\n", "        current_ids = torch.cat([current_ids, next_token_id], dim=1)\n", "        \n", "        # Update attention mask\n", "        attention_mask = torch.cat([\n", "            attention_mask, \n", "            torch.ones((1, 1), device=device, dtype=attention_mask.dtype)\n", "        ], dim=1)\n", "        \n", "        # Stop if we hit EOS token\n", "        if next_token_id.item() == tokenizer.eos_token_id:\n", "            break\n", "    \n", "    return generation_data\n", "\n", "print(\"Generation functions defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_generation_attention(generation_data, head_idx=0):\n", "    \"\"\"Analyze attention patterns for each generated token.\"\"\"\n", "    \n", "    if not generation_data['generated_tokens']:\n", "        print(\"No tokens were generated.\")\n", "        return None\n", "    \n", "    input_length = generation_data['input_length']\n", "    input_tokens = generation_data['input_tokens']\n", "    generated_tokens = generation_data['generated_tokens']\n", "    \n", "    print(f\"🎯 GENERATION ATTENTION ANALYSIS (Head {head_idx})\")\n", "    print(f\"Input length: {input_length} tokens\")\n", "    print(f\"Generated: {len(generated_tokens)} tokens\")\n", "    \n", "    generated_text = tokenizer.convert_tokens_to_string(generated_tokens)\n", "    print(f\"Generated text: \\\"{generated_text}\\\"\")\n", "    \n", "    # Analyze each generated token\n", "    for step, (token, attention_weights) in enumerate(zip(generated_tokens, generation_data['generation_attentions'])):\n", "        print(f\"\\n📍 Step {step + 1}: Generated '{token}'\")\n", "        \n", "        # Get attention weights for the specified head\n", "        head_attention = attention_weights[head_idx]  # [seq_len]\n", "        \n", "        # Focus on attention to input tokens only\n", "        input_attention = head_attention[:input_length]\n", "        \n", "        # Get top attended input tokens\n", "        top_indices = np.argsort(input_attention)[-5:][::-1]  # Top 5\n", "        \n", "        print(\"   Top attended input tokens:\")\n", "        for i, idx in enumerate(top_indices):\n", "            if idx < len(input_tokens):\n", "                token_text = tokenizer.convert_tokens_to_string([input_tokens[idx]])\n", "                print(f\"   {i+1}. '{token_text}' (pos {idx}) - {input_attention[idx]:.4f}\")\n", "    \n", "    return {\n", "        'input_tokens': input_tokens,\n", "        'generated_tokens': generated_tokens,\n", "        'attention_data': generation_data['generation_attentions'],\n", "        'input_length': input_length\n", "    }\n", "\n", "print(\"Analysis functions defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_scenario_comparison(example_idx=0, layer_idx=-1, head_idx=0, max_tokens=5):\n", "    \"\"\"Run generation analysis for both scheming and baseline scenarios.\"\"\"\n", "    \n", "    if example_idx >= len(dataset):\n", "        print(f\"Example index {example_idx} out of range. Dataset has {len(dataset)} examples.\")\n", "        return None\n", "    \n", "    example = dataset[example_idx]\n", "    \n", "    print(\"=\" * 80)\n", "    print(f\"GENERATION ATTENTION ANALYSIS - EXAMPLE {example_idx}\")\n", "    print(\"=\" * 80)\n", "    print(f\"Pressure Category: {example['pressure_category']}\")\n", "    print(f\"Pressure Source: {example['pressure_source']}\")\n", "    print(f\"Ground Truth: {example['ground_truth']}\")\n", "    print(f\"Expected Honest: {example['honest_answer']}\")\n", "    print(f\"Expected Dishonest: {example['dishonest_answer']}\")\n", "    print(f\"Layer: {layer_idx} | Head: {head_idx}\")\n", "    \n", "    # Prepare prompts\n", "    scheming_prompt = apply_chat_template(example['scheming_system_prompt'], example['user_prompt'])\n", "    baseline_prompt = apply_chat_template(example['baseline_system_prompt'], example['user_prompt'])\n", "    \n", "    results = {\n", "        'example_idx': example_idx,\n", "        'layer_idx': layer_idx,\n", "        'head_idx': head_idx,\n", "        'metadata': {\n", "            'pressure_category': example['pressure_category'],\n", "            'honest_answer': example['honest_answer'],\n", "            'dishonest_answer': example['dishonest_answer']\n", "        }\n", "    }\n", "    \n", "    # Analyze scheming scenario\n", "    print(f\"\\n🔴 ANALYZING SCHEMING SCENARIO...\")\n", "    print(f\"System: {example['scheming_system_prompt']}\")\n", "    print(f\"User: {example['user_prompt']}\")\n", "    \n", "    scheming_data = generate_with_attention(scheming_prompt, max_tokens)\n", "    scheming_analysis = analyze_generation_attention(scheming_data, head_idx)\n", "    results['scheming'] = {\n", "        'generation_data': scheming_data,\n", "        'analysis': scheming_analysis,\n", "        'prompt': scheming_prompt\n", "    }\n", "    \n", "    # Analyze baseline scenario\n", "    print(f\"\\n🔵 ANALYZING BASELINE SCENARIO...\")\n", "    print(f\"System: {example['baseline_system_prompt']}\")\n", "    print(f\"User: {example['user_prompt']}\")\n", "    \n", "    baseline_data = generate_with_attention(baseline_prompt, max_tokens)\n", "    baseline_analysis = analyze_generation_attention(baseline_data, head_idx)\n", "    results['baseline'] = {\n", "        'generation_data': baseline_data,\n", "        'analysis': baseline_analysis,\n", "        'prompt': baseline_prompt\n", "    }\n", "    \n", "    return results\n", "\n", "print(\"Scenario comparison function defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Analysis Controls"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create interactive controls\n", "example_slider = widgets.IntSlider(\n", "    value=0,\n", "    min=0,\n", "    max=min(len(dataset) - 1, 50),  # Limit to first 50 examples for performance\n", "    step=1,\n", "    description='Example:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "layer_slider = widgets.IntSlider(\n", "    value=-1,\n", "    min=-1,\n", "    max=31,  # Llama-3.1-8B has 32 layers (0-31), -1 means last layer\n", "    step=1,\n", "    description='Layer (-1=last):',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "head_slider = widgets.IntSlider(\n", "    value=0,\n", "    min=0,\n", "    max=31,  # <PERSON>lama-3.1-8B has 32 attention heads (0-31)\n", "    step=1,\n", "    description='Attention Head:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "max_tokens_slider = widgets.IntSlider(\n", "    value=5,\n", "    min=1,\n", "    max=20,\n", "    step=1,\n", "    description='<PERSON>:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Display controls\n", "display(widgets.VBox([\n", "    widgets.HTML('<h3>🎛️ Analysis Parameters</h3>'),\n", "    example_slider,\n", "    layer_slider,\n", "    head_slider,\n", "    max_tokens_slider,\n", "    widgets.HTML('<p><strong>Instructions:</strong> Adjust the sliders above, then run the cell below to analyze attention patterns.</p>')\n", "]))\n", "\n", "print(\"Interactive controls ready! Adjust the sliders above and run the next cell.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the analysis with current slider values\n", "results = run_scenario_comparison(\n", "    example_slider.value, \n", "    layer_slider.value, \n", "    head_slider.value,\n", "    max_tokens_slider.value\n", ")\n", "\n", "if results:\n", "    print(\"\\n✅ Generation attention analysis complete!\")\n", "    \n", "    # Analyze the generated responses\n", "    if (results['scheming']['generation_data']['generated_tokens'] and \n", "        results['baseline']['generation_data']['generated_tokens']):\n", "        \n", "        scheming_response = tokenizer.convert_tokens_to_string(results['scheming']['generation_data']['generated_tokens'])\n", "        baseline_response = tokenizer.convert_tokens_to_string(results['baseline']['generation_data']['generated_tokens'])\n", "        \n", "        print(f\"\\n📝 RESPONSE ANALYSIS:\")\n", "        print(f\"🔴 Scheming response: '{scheming_response}'\")\n", "        print(f\"🔵 Baseline response: '{baseline_response}'\")\n", "        \n", "        # Check if the model is giving a generic response\n", "        if 'You are' in scheming_response or 'I am' in scheming_response:\n", "            print(f\"\\n⚠️  ANALYSIS: The model appears to be giving a generic/safe response in the scheming scenario.\")\n", "            print(f\"    This often happens when models encounter conflicting instructions or pressure.\")\n", "            print(f\"    The model may be falling back to repeating parts of the system prompt rather than\")\n", "            print(f\"    directly answering the question. This is actually a form of resistance to pressure!\")\n", "        \n", "        # Check if responses contain the expected answers\n", "        honest_answer = results['metadata']['honest_answer']\n", "        dishonest_answer = results['metadata']['dishonest_answer']\n", "        \n", "        if honest_answer.lower() in baseline_response.lower():\n", "            print(f\"\\n✓ Baseline contains expected honest answer: '{honest_answer}'\")\n", "        \n", "        if dishonest_answer.lower() in scheming_response.lower():\n", "            print(f\"\\n⚠️  Scheming contains expected dishonest answer: '{dishonest_answer}'\")\n", "        elif honest_answer.lower() in scheming_response.lower():\n", "            print(f\"\\n✓ Scheming actually contains honest answer: '{honest_answer}' (pressure failed!)\")\n", "        else:\n", "            print(f\"\\n🤔 Scheming response doesn't contain expected answer - model may be deflecting\")\n", "    \n", "    print(\"\\n📊 Scroll up to see detailed attention analysis for each generated token.\")\n", "else:\n", "    print(\"❌ Analysis failed. Please check the example index and try again.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}