# Import required libraries
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import seaborn as sns
import pandas as pd
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from IPython.display import display, HTML
import ipywidgets as widgets
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 8)
plt.rcParams['font.size'] = 10

print("Libraries loaded successfully!")

# Configuration
DATASET_PATH = "../../dataset_creation/squad_scheming_dataset.jsonl"
MODEL_NAME = "meta-llama/Llama-3.1-8B"

# Load model and tokenizer
print(f"Loading model: {MODEL_NAME}")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    output_attentions=True,
    attn_implementation="eager",  # Required for attention extraction
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto" if torch.cuda.is_available() else None
)

# Add padding token if not present
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

device = next(model.parameters()).device
print(f"Model loaded on device: {device}")
print(f"Model has {model.config.num_hidden_layers} layers and {model.config.num_attention_heads} attention heads")

# Load dataset
def load_dataset(path):
    data = []
    with open(path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    return data

dataset = load_dataset(DATASET_PATH)
print(f"Loaded {len(dataset)} examples from dataset")

# Display first example structure
print("\nExample structure:")
example = dataset[0]
for key, value in example.items():
    if isinstance(value, str) and len(value) > 100:
        print(f"{key}: {value[:100]}...")
    else:
        print(f"{key}: {value}")

def create_chat_prompt(system_prompt, user_prompt):
    """Create a chat-formatted prompt for Llama models."""
    # Try to use chat template if available, otherwise create simple format
    try:
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        return tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
    except ValueError:
        # Fallback to simple format if chat template is not available
        return f"System: {system_prompt}\n\nUser: {user_prompt}\n\nAssistant:"

def generate_with_attention(prompt, max_new_tokens=5, layer_idx=-1, head_idx=0):
    """Generate text while capturing attention patterns for each generated token."""
    
    # Tokenize input
    inputs = tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
    input_ids = inputs["input_ids"].to(device)
    attention_mask = inputs["attention_mask"].to(device)
    
    input_length = input_ids.shape[1]
    input_tokens = tokenizer.convert_ids_to_tokens(input_ids[0])
    
    # Storage for generation data
    generated_tokens = []
    generation_attentions = []
    
    # Generate tokens one by one
    current_ids = input_ids.clone()
    
    for step in range(max_new_tokens):
        with torch.no_grad():
            outputs = model(
                current_ids,
                attention_mask=attention_mask,
                output_attentions=True,
                use_cache=False
            )
        
        # Get next token
        logits = outputs.logits[0, -1, :]
        next_token_id = torch.argmax(logits, dim=-1).unsqueeze(0).unsqueeze(0)
        next_token = tokenizer.convert_ids_to_tokens([next_token_id.item()])[0]
        
        # Store generated token
        generated_tokens.append(next_token)
        
        # Extract attention from the specified layer and head
        # attentions shape: [batch, heads, seq_len, seq_len]
        layer_attention = outputs.attentions[layer_idx][0]  # [heads, seq_len, seq_len]
        head_attention = layer_attention[head_idx]  # [seq_len, seq_len]
        
        # Get attention from the last position (newly generated token) to all previous positions
        generation_attention = head_attention[-1, :].cpu().numpy()  # [seq_len]
        generation_attentions.append(generation_attention)
        
        # Update current_ids for next iteration
        current_ids = torch.cat([current_ids, next_token_id], dim=1)
        
        # Update attention mask
        attention_mask = torch.cat([
            attention_mask, 
            torch.ones((1, 1), device=device, dtype=attention_mask.dtype)
        ], dim=1)
        
        # Stop if we hit EOS token
        if next_token_id.item() == tokenizer.eos_token_id:
            break
    
    return {
        'input_tokens': input_tokens,
        'input_length': input_length,
        'generated_tokens': generated_tokens,
        'generation_attentions': generation_attentions,
        'prompt': prompt
    }

def analyze_generation_attention(example_idx, layer_idx=-1, head_idx=0, max_tokens=5):
    """Analyze attention patterns for both scheming and baseline scenarios."""
    
    if example_idx >= len(dataset):
        print(f"Example index {example_idx} out of range. Dataset has {len(dataset)} examples.")
        return None
    
    example = dataset[example_idx]
    
    print(f"\n{'='*80}")
    print(f"GENERATION ATTENTION ANALYSIS - EXAMPLE {example_idx}")
    print(f"{'='*80}")
    print(f"Pressure Category: {example.get('pressure_category', 'N/A')}")
    print(f"Pressure Source: {example.get('pressure_source', 'N/A')}")
    print(f"Ground Truth: {example.get('ground_truth', example.get('answer', 'N/A'))}")
    
    # Check for different possible key names for honest/dishonest answers
    honest_key = None
    dishonest_key = None
    
    for key in example.keys():
        if 'honest' in key.lower():
            honest_key = key
        elif 'dishonest' in key.lower():
            dishonest_key = key
    
    honest_answer = example.get(honest_key, example.get('answer', 'N/A')) if honest_key else example.get('answer', 'N/A')
    dishonest_answer = example.get(dishonest_key, 'N/A') if dishonest_key else 'N/A'
    
    print(f"Expected Honest: {honest_answer}")
    print(f"Expected Dishonest: {dishonest_answer}")
    print(f"Layer: {layer_idx} | Head: {head_idx}")
    
    # Debug: Print all available keys
    print(f"\nAvailable keys in example: {list(example.keys())}")
    
    # Create prompts - handle different possible key names
    scheming_system = example.get('scheming_system_prompt', example.get('system_prompt_scheming', example.get('system_prompt', 'You are a helpful assistant.')))
    baseline_system = example.get('baseline_system_prompt', example.get('system_prompt_baseline', example.get('system_prompt', 'You are a helpful assistant.')))
    user_prompt = example.get('user_prompt', example.get('prompt', example.get('question', 'Hello')))
    
    scheming_prompt = create_chat_prompt(scheming_system, user_prompt)
    baseline_prompt = create_chat_prompt(baseline_system, user_prompt)
    
    # Generate and analyze scheming scenario
    print(f"\n🔴 ANALYZING SCHEMING SCENARIO...")
    print(f"System: {scheming_system[:100]}...")
    print(f"User: {user_prompt}")
    
    scheming_data = generate_with_attention(scheming_prompt, max_tokens, layer_idx, head_idx)
    
    # Generate and analyze baseline scenario
    print(f"\n🔵 ANALYZING BASELINE SCENARIO...")
    print(f"System: {baseline_system}")
    print(f"User: {user_prompt}")
    
    baseline_data = generate_with_attention(baseline_prompt, max_tokens, layer_idx, head_idx)
    
    return {
        'scheming': {
            'generation_data': scheming_data,
            'system_prompt': scheming_system,
            'user_prompt': user_prompt
        },
        'baseline': {
            'generation_data': baseline_data,
            'system_prompt': baseline_system,
            'user_prompt': user_prompt
        },
        'metadata': {
            'example_idx': example_idx,
            'pressure_category': example.get('pressure_category', 'N/A'),
            'honest_answer': honest_answer,
            'dishonest_answer': dishonest_answer,
            'ground_truth': example.get('ground_truth', example.get('answer', 'N/A'))
        },
        'layer_idx': layer_idx,
        'head_idx': head_idx
    }

print("Core generation and attention analysis functions defined!")

def print_generation_attention_analysis(generation_data, head_idx=0, scenario_name=""):
    """Print detailed attention analysis for each generated token."""
    
    if not generation_data['generated_tokens']:
        print("No tokens were generated.")
        return
    
    input_tokens = generation_data['input_tokens']
    generated_tokens = generation_data['generated_tokens']
    generation_attentions = generation_data['generation_attentions']
    input_length = generation_data['input_length']
    
    generated_text = tokenizer.convert_tokens_to_string(generated_tokens)
    
    print(f"\n🎯 GENERATION ATTENTION ANALYSIS (Head {head_idx})")
    print(f"Input length: {input_length} tokens")
    print(f"Generated: {len(generated_tokens)} tokens")
    print(f"Generated text: \"{generated_text}\"")
    
    # Analyze each generated token
    for step, (token, attention_weights) in enumerate(zip(generated_tokens, generation_attentions)):
        print(f"\n📍 Step {step + 1}: Generated '{token}'")
        
        # Get attention to input tokens only
        input_attention = attention_weights[:input_length]
        
        # Find top attended input tokens
        top_indices = np.argsort(input_attention)[-5:][::-1]  # Top 5
        
        print(f"   Top attended input tokens:")
        for i, idx in enumerate(top_indices):
            if idx < len(input_tokens):
                token_text = tokenizer.convert_tokens_to_string([input_tokens[idx]])
                print(f"   {i+1:2d}. '{token_text}' (pos {idx}) - {input_attention[idx]:.4f}")

def create_colored_attention_visualization(generation_data, target_gen_step=0, head_idx=0, title=""):
    """Create colored visualization showing attention from a specific generated token to input."""
    
    if target_gen_step >= len(generation_data['generation_attentions']):
        print(f"Generation step {target_gen_step} out of range.")
        return None
    
    input_tokens = generation_data['input_tokens']
    generated_tokens = generation_data['generated_tokens']
    attention_weights = generation_data['generation_attentions'][target_gen_step]
    input_length = generation_data['input_length']
    
    # Get attention to input tokens only
    input_attention = attention_weights[:input_length]
    
    # Normalize attention weights to [0, 1]
    if len(input_attention) > 0:
        min_att = np.min(input_attention)
        max_att = np.max(input_attention)
        if max_att > min_att:
            normalized_weights = (input_attention - min_att) / (max_att - min_att)
        else:
            normalized_weights = np.ones_like(input_attention) * 0.5
    else:
        normalized_weights = np.array([])
    
    # Create colormap
    cmap = plt.cm.get_cmap('Reds')
    
    # Generate HTML
    target_token = generated_tokens[target_gen_step] if target_gen_step < len(generated_tokens) else "N/A"
    target_text = tokenizer.convert_tokens_to_string([target_token])
    
    html_parts = []
    html_parts.append(f'<div style="font-family: monospace; font-size: 14px; line-height: 1.8; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #fafafa; max-width: 100%; overflow-wrap: break-word;">')
    html_parts.append(f'<h3 style="margin-top: 0; color: #333;">{title} (Head {head_idx})</h3>')
    html_parts.append(f'<div style="margin-bottom: 10px; font-weight: bold;">Attention from Generated Token \"{target_text}\" (Step {target_gen_step + 1}) to Input:</div>')
    
    for i, token in enumerate(input_tokens):
        if i < len(normalized_weights):
            # Get color from colormap
            rgba = cmap(normalized_weights[i])
            color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
            
            # Clean token for display
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]
            
            # Create colored span with tooltip
            html_parts.append(
                f'<span style="background-color: {color}; padding: 2px 4px; margin: 1px; '
                f'border-radius: 3px; display: inline-block; word-break: break-all;" '
                f'title="Token: {display_token}\\nAttention: {input_attention[i]:.4f}\\nNormalized: {normalized_weights[i]:.4f}\\nPosition: {i}">{display_token}</span>'
            )
        else:
            # Token without attention weight
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]
            html_parts.append(f'<span style="padding: 2px 4px; margin: 1px; word-break: break-all;">{display_token}</span>')
    
    # Add colorbar legend
    html_parts.append('<div style="margin-top: 15px; font-size: 12px;">')
    html_parts.append('<strong>Attention Scale:</strong> ')
    
    # Create mini colorbar
    for i in range(10):
        intensity = i / 9.0
        rgba = cmap(intensity)
        color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
        html_parts.append(f'<span style="background-color: {color}; width: 20px; height: 15px; display: inline-block; margin: 1px;"></span>')
    
    if len(input_attention) > 0:
        html_parts.append(f'<br><span style="font-size: 10px;">Min: {min_att:.4f} | Max: {max_att:.4f}</span>')
    
    html_parts.append('</div>')
    html_parts.append('</div>')
    
    return ''.join(html_parts)

def create_attention_heatmap(generation_data, head_idx=0, title="Generation Attention Heatmap"):
    """Create interactive heatmap showing attention from all generated tokens to input."""
    
    if not generation_data['generated_tokens']:
        print("No tokens were generated.")
        return None
    
    input_tokens = generation_data['input_tokens']
    generated_tokens = generation_data['generated_tokens']
    generation_attentions = generation_data['generation_attentions']
    input_length = generation_data['input_length']
    
    # Create attention matrix: [num_generated_tokens, input_length]
    attention_matrix = []
    for attention_weights in generation_attentions:
        input_attention = attention_weights[:input_length]
        attention_matrix.append(input_attention)
    
    attention_matrix = np.array(attention_matrix)
    
    # Prepare labels
    input_labels = [tokenizer.convert_tokens_to_string([token]).strip() for token in input_tokens[:input_length]]
    gen_labels = [f"Gen{i+1}: {tokenizer.convert_tokens_to_string([token]).strip()}" for i, token in enumerate(generated_tokens)]
    
    # Create heatmap
    fig = go.Figure(data=go.Heatmap(
        z=attention_matrix,
        x=input_labels,
        y=gen_labels,
        colorscale='Reds',
        hovertemplate='<b>Generated:</b> %{y}<br><b>Input:</b> %{x}<br><b>Attention:</b> %{z:.4f}<extra></extra>'
    ))
    
    fig.update_layout(
        title=f"{title} (Head {head_idx})",
        xaxis_title="Input Tokens",
        yaxis_title="Generated Tokens",
        width=max(800, len(input_labels) * 20),
        height=max(400, len(gen_labels) * 50)
    )
    
    fig.update_xaxes(tickangle=-45)
    
    return fig

print("Per-token attention analysis and visualization functions defined!")

def calculate_average_generation_attention(generation_data, head_idx=0):
    """Calculate average attention across all generated tokens."""
    
    if not generation_data['generated_tokens']:
        print("No generation data available.")
        return None
    
    input_length = generation_data['input_length']
    input_tokens = generation_data['input_tokens']
    generated_tokens = generation_data['generated_tokens']
    
    # Collect attention weights for all generated tokens
    all_attention_weights = []
    
    for attention_weights in generation_data['generation_attentions']:
        input_attention = attention_weights[:input_length]  # Only attention to input tokens
        all_attention_weights.append(input_attention)
    
    # Calculate average attention across all generated tokens
    if all_attention_weights:
        average_attention = np.mean(all_attention_weights, axis=0)  # [input_length]
        
        # Find top attended input tokens
        top_indices = np.argsort(average_attention)[-10:][::-1]  # Top 10
        
        analysis = {
            'average_attention': average_attention,
            'input_tokens': input_tokens,
            'generated_tokens': generated_tokens,
            'top_attended_tokens': []
        }
        
        for idx in top_indices:
            if idx < len(input_tokens):
                analysis['top_attended_tokens'].append({
                    'token': input_tokens[idx],
                    'text': tokenizer.convert_tokens_to_string([input_tokens[idx]]),
                    'attention': average_attention[idx],
                    'position': idx
                })
        
        return analysis
    
    return None

def create_average_attention_visualization(generation_data, head_idx=0, title="Average Attention Across All Generated Tokens"):
    """Create colored visualization showing average attention across all generated tokens."""
    
    analysis = calculate_average_generation_attention(generation_data, head_idx)
    
    if not analysis:
        print("No analysis data available.")
        return None
    
    input_tokens = analysis['input_tokens']
    average_attention = analysis['average_attention']
    generated_tokens = analysis['generated_tokens']
    
    # Normalize attention weights to [0, 1]
    if len(average_attention) > 0:
        min_att = np.min(average_attention)
        max_att = np.max(average_attention)
        if max_att > min_att:
            normalized_weights = (average_attention - min_att) / (max_att - min_att)
        else:
            normalized_weights = np.ones_like(average_attention) * 0.5
    else:
        normalized_weights = np.array([])
    
    # Create colormap (use purple to distinguish from single-token analysis)
    cmap = plt.cm.get_cmap('Purples')
    
    # Generate HTML
    generated_text = tokenizer.convert_tokens_to_string(generated_tokens)
    html_parts = []
    html_parts.append(f'<div style="font-family: monospace; font-size: 14px; line-height: 1.8; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #fafafa; max-width: 100%; overflow-wrap: break-word;">')
    html_parts.append(f'<h3 style="margin-top: 0; color: #333;">{title} (Head {head_idx})</h3>')
    html_parts.append(f'<div style="margin-bottom: 10px; font-weight: bold;">Generated Response: \"{generated_text}\"</div>')
    html_parts.append(f'<div style="margin-bottom: 15px; font-weight: bold;">Average Attention Across {len(generated_tokens)} Generated Tokens:</div>')
    
    for i, token in enumerate(input_tokens):
        if i < len(normalized_weights):
            # Get color from colormap
            rgba = cmap(normalized_weights[i])
            color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
            
            # Clean token for display
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]
            
            # Create colored span with tooltip
            html_parts.append(
                f'<span style="background-color: {color}; padding: 2px 4px; margin: 1px; '
                f'border-radius: 3px; display: inline-block; word-break: break-all;" '
                f'title="Token: {display_token}\\nAverage Attention: {average_attention[i]:.4f}\\nNormalized: {normalized_weights[i]:.4f}\\nPosition: {i}">{display_token}</span>'
            )
        else:
            # Token without attention weight
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]
            html_parts.append(f'<span style="padding: 2px 4px; margin: 1px; word-break: break-all;">{display_token}</span>')
    
    # Add colorbar legend
    html_parts.append('<div style="margin-top: 15px; font-size: 12px;">')
    html_parts.append('<strong>Average Attention Scale:</strong> ')
    
    # Create mini colorbar
    for i in range(10):
        intensity = i / 9.0
        rgba = cmap(intensity)
        color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
        html_parts.append(f'<span style="background-color: {color}; width: 20px; height: 15px; display: inline-block; margin: 1px;"></span>')
    
    if len(average_attention) > 0:
        html_parts.append(f'<br><span style="font-size: 10px;">Min: {min_att:.4f} | Max: {max_att:.4f}</span>')
    
    html_parts.append('</div>')
    html_parts.append('</div>')
    
    return ''.join(html_parts), analysis

def create_average_attention_comparison_plot(scheming_analysis, baseline_analysis, head_idx=0):
    """Create comparison plot of average attention patterns between scenarios."""
    
    if not scheming_analysis or not baseline_analysis:
        print("Missing analysis data for comparison.")
        return None
    
    # Get top tokens from both scenarios
    scheming_top = scheming_analysis['top_attended_tokens'][:15]
    baseline_top = baseline_analysis['top_attended_tokens'][:15]
    
    # Create comparison plot
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=["🔴 Scheming - Top Average Attention", "🔵 Baseline - Top Average Attention"],
        specs=[[{"type": "bar"}, {"type": "bar"}]]
    )
    
    # Scheming scenario
    scheming_tokens = [item['text'][:15] for item in scheming_top]
    scheming_attentions = [item['attention'] for item in scheming_top]
    
    fig.add_trace(
        go.Bar(
            x=scheming_tokens,
            y=scheming_attentions,
            name="Scheming",
            marker_color='red',
            hovertemplate='<b>Token:</b> %{x}<br><b>Avg Attention:</b> %{y:.4f}<extra></extra>'
        ),
        row=1, col=1
    )
    
    # Baseline scenario
    baseline_tokens = [item['text'][:15] for item in baseline_top]
    baseline_attentions = [item['attention'] for item in baseline_top]
    
    fig.add_trace(
        go.Bar(
            x=baseline_tokens,
            y=baseline_attentions,
            name="Baseline",
            marker_color='blue',
            hovertemplate='<b>Token:</b> %{x}<br><b>Avg Attention:</b> %{y:.4f}<extra></extra>'
        ),
        row=1, col=2
    )
    
    fig.update_layout(
        title=f"Average Attention Comparison Across All Generated Tokens (Head {head_idx})",
        showlegend=False,
        height=500
    )
    
    fig.update_xaxes(tickangle=-45)
    fig.update_yaxes(title_text="Average Attention")
    
    return fig

print("Average attention analysis functions defined!")

# Create interactive controls
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=min(len(dataset) - 1, 50),  # Limit to first 50 examples for performance
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

layer_slider = widgets.IntSlider(
    value=-1,
    min=-1,
    max=31,  # Llama-3.1-8B has 32 layers (0-31), -1 means last layer
    step=1,
    description='Layer:',
    style={'description_width': 'initial'}
)

head_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=31,  # Llama-3.1-8B has 32 attention heads (0-31)
    step=1,
    description='Head:',
    style={'description_width': 'initial'}
)

max_tokens_slider = widgets.IntSlider(
    value=5,
    min=1,
    max=20,
    step=1,
    description='Max Tokens:',
    style={'description_width': 'initial'}
)

# Display controls
display(widgets.VBox([
    widgets.HTML('<h3>🎛️ Analysis Controls</h3>'),
    widgets.HBox([example_slider, layer_slider]),
    widgets.HBox([head_slider, max_tokens_slider]),
    widgets.HTML('<p><strong>Instructions:</strong> Adjust the sliders above and run the cell below to analyze attention patterns.</p>')
]))

print("Interactive controls created!")

# Run the analysis with current slider values
results = analyze_generation_attention(
    example_slider.value, 
    layer_slider.value, 
    head_slider.value,
    max_tokens_slider.value
)

if results:
    # Print detailed attention analysis for both scenarios
    print_generation_attention_analysis(
        results['scheming']['generation_data'], 
        results['head_idx'], 
        "Scheming"
    )
    
    print_generation_attention_analysis(
        results['baseline']['generation_data'], 
        results['head_idx'], 
        "Baseline"
    )
    
    print("\n✅ Generation attention analysis complete!")
    
    # Analyze the generated responses
    if (results['scheming']['generation_data']['generated_tokens'] and 
        results['baseline']['generation_data']['generated_tokens']):
        
        scheming_response = tokenizer.convert_tokens_to_string(results['scheming']['generation_data']['generated_tokens'])
        baseline_response = tokenizer.convert_tokens_to_string(results['baseline']['generation_data']['generated_tokens'])
        
        print(f"\n📝 RESPONSE ANALYSIS:")
        print(f"🔴 Scheming response: '{scheming_response}'")
        print(f"🔵 Baseline response: '{baseline_response}'")
        
        # Check if the model is giving a generic response
        if 'You are' in scheming_response or 'I am' in scheming_response:
            print(f"\n⚠️  ANALYSIS: The model appears to be giving a generic/safe response in the scheming scenario.")
            print(f"    This often happens when models encounter conflicting instructions or pressure.")
            print(f"    The model may be falling back to repeating parts of the system prompt rather than")
            print(f"    directly answering the question. This is actually a form of resistance to pressure!")
        
        # Check if responses contain the expected answers
        honest_answer = results['metadata']['honest_answer']
        dishonest_answer = results['metadata']['dishonest_answer']
        
        if honest_answer.lower() in baseline_response.lower():
            print(f"\n✓ Baseline contains expected honest answer: '{honest_answer}'")
        
        if dishonest_answer.lower() in scheming_response.lower():
            print(f"\n⚠️  Scheming contains expected dishonest answer: '{dishonest_answer}'")
        elif honest_answer.lower() in scheming_response.lower():
            print(f"\n✓ Scheming actually contains honest answer: '{honest_answer}' (pressure failed!)")
        else:
            print(f"\n🤔 Scheming response doesn't contain expected answer - model may be deflecting")
    
    print("\n📊 Scroll up to see detailed attention analysis for each generated token.")
else:
    print("❌ Analysis failed. Please check the example index and try again.")

# Create colored visualizations for the first generated token
if 'results' in locals() and results:
    print("\n" + "="*100)
    print("COLORED TOKEN VISUALIZATIONS - FIRST GENERATED TOKEN")
    print("="*100)
    
    # Scheming scenario visualization
    scheming_html = create_colored_attention_visualization(
        results['scheming']['generation_data'],
        target_gen_step=0,
        head_idx=results['head_idx'],
        title="🔴 Scheming - First Token Attention"
    )
    
    if scheming_html:
        display(HTML(scheming_html))
    
    # Baseline scenario visualization
    baseline_html = create_colored_attention_visualization(
        results['baseline']['generation_data'],
        target_gen_step=0,
        head_idx=results['head_idx'],
        title="🔵 Baseline - First Token Attention"
    )
    
    if baseline_html:
        display(HTML(baseline_html))
    
    print("\n💡 Interpretation:")
    print("- Darker red colors indicate higher attention")
    print("- Compare which words get attention in scheming vs baseline scenarios")
    print("- Look for attention to authority words vs factual content")
    
else:
    print("❌ No analysis results available. Please run the analysis first.")

# Create interactive heatmaps
if 'results' in locals() and results:
    print("\n📊 INTERACTIVE ATTENTION HEATMAPS")
    
    # Scheming heatmap
    scheming_heatmap = create_attention_heatmap(
        results['scheming']['generation_data'],
        results['head_idx'],
        "🔴 Scheming - Generation Attention Matrix"
    )
    
    if scheming_heatmap:
        scheming_heatmap.show()
    
    # Baseline heatmap
    baseline_heatmap = create_attention_heatmap(
        results['baseline']['generation_data'],
        results['head_idx'],
        "🔵 Baseline - Generation Attention Matrix"
    )
    
    if baseline_heatmap:
        baseline_heatmap.show()
    
    print("\n💡 How to read the heatmaps:")
    print("- Y-axis: Generated tokens (Gen1, Gen2, etc.)")
    print("- X-axis: Input tokens")
    print("- Color intensity: Attention strength")
    print("- Hover over cells for exact attention values")
    
else:
    print("❌ No analysis results available. Please run the analysis first.")

# Analyze average attention across all generated tokens
if 'results' in locals() and results:
    print("\n" + "="*100)
    print("AVERAGE ATTENTION ANALYSIS ACROSS ALL GENERATED TOKENS")
    print("="*100)
    
    # Calculate average attention for both scenarios
    scheming_avg_analysis = calculate_average_generation_attention(
        results['scheming']['generation_data'], 
        results['head_idx']
    )
    
    baseline_avg_analysis = calculate_average_generation_attention(
        results['baseline']['generation_data'], 
        results['head_idx']
    )
    
    if scheming_avg_analysis and baseline_avg_analysis:
        # Print top attended tokens for each scenario
        print(f"\n🔴 SCHEMING SCENARIO - TOP AVERAGE ATTENTION:")
        scheming_response = tokenizer.convert_tokens_to_string(scheming_avg_analysis['generated_tokens'])
        print(f"Generated Response: \"{scheming_response}\"")
        print(f"Top attended input tokens (averaged across all {len(scheming_avg_analysis['generated_tokens'])} generated tokens):")
        
        for i, token_info in enumerate(scheming_avg_analysis['top_attended_tokens'][:10]):
            print(f"   {i+1:2d}. '{token_info['text']}' (pos {token_info['position']}) - {token_info['attention']:.4f}")
        
        print(f"\n🔵 BASELINE SCENARIO - TOP AVERAGE ATTENTION:")
        baseline_response = tokenizer.convert_tokens_to_string(baseline_avg_analysis['generated_tokens'])
        print(f"Generated Response: \"{baseline_response}\"")
        print(f"Top attended input tokens (averaged across all {len(baseline_avg_analysis['generated_tokens'])} generated tokens):")
        
        for i, token_info in enumerate(baseline_avg_analysis['top_attended_tokens'][:10]):
            print(f"   {i+1:2d}. '{token_info['text']}' (pos {token_info['position']}) - {token_info['attention']:.4f}")
        
        # Create colored visualizations
        print(f"\n📊 AVERAGE ATTENTION VISUALIZATIONS:")
        
        scheming_avg_html, _ = create_average_attention_visualization(
            results['scheming']['generation_data'],
            results['head_idx'],
            "🔴 Scheming - Average Attention Across All Generated Tokens"
        )
        
        if scheming_avg_html:
            display(HTML(scheming_avg_html))
        
        baseline_avg_html, _ = create_average_attention_visualization(
            results['baseline']['generation_data'],
            results['head_idx'],
            "🔵 Baseline - Average Attention Across All Generated Tokens"
        )
        
        if baseline_avg_html:
            display(HTML(baseline_avg_html))
        
        # Create comparison plot
        print(f"\n📈 AVERAGE ATTENTION COMPARISON PLOT:")
        comparison_fig = create_average_attention_comparison_plot(
            scheming_avg_analysis, 
            baseline_avg_analysis, 
            results['head_idx']
        )
        
        if comparison_fig:
            comparison_fig.show()
        
        print(f"\n✅ Average attention analysis complete!")
        print(f"\n💡 Key insights:")
        print(f"- Purple coloring shows average attention across ALL generated tokens")
        print(f"- Compare which input words get consistent attention throughout the response")
        print(f"- Look for systematic differences between scheming and baseline scenarios")
        print(f"- Darker purple = higher average attention across the entire response")
        
    else:
        print("❌ Could not calculate average attention. Check if tokens were generated.")
        
else:
    print("❌ No analysis results available. Please run the generation analysis first.")