{
    "cells": [
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "# Generation Attention Analysis: What Does the Model Look At When Generating Responses?\n",
                "\n",
                "This notebook analyzes attention patterns **during text generation** to understand what parts of the input the model focuses on when generating specific output tokens like \"Yes\", \"No\", or factual answers.\n",
                "\n",
                "## Key Question:\n",
                "**When the model generates \"Yes\", what input tokens is it paying attention to?**\n",
                "\n",
                "## Analysis Goals:\n",
                "- **Generation-time attention**: Capture attention weights during token generation\n",
                "- **Token-by-token analysis**: See what each generated token attends to\n",
                "- **Input-output mapping**: Understand which input words influence specific output words\n",
                "- **Pressure vs. baseline comparison**: How attention changes under pressure\n",
                "\n",
                "## Expected Insights:\n",
                "- When generating dishonest answers, does the model attend more to pressure cues?\n",
                "- When generating honest answers, does the model attend more to factual content?\n",
                "- Which specific words in the system/user prompt drive dishonest responses?"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Import required libraries\n",
                "import json\n",
                "import numpy as np\n",
                "import matplotlib.pyplot as plt\n",
                "import matplotlib.colors as mcolors\n",
                "import seaborn as sns\n",
                "import pandas as pd\n",
                "import torch\n",
                "from transformers import AutoTokenizer, AutoModelForCausalLM\n",
                "from IPython.display import display, HTML\n",
                "import ipywidgets as widgets\n",
                "import plotly.graph_objects as go\n",
                "import plotly.express as px\n",
                "from plotly.subplots import make_subplots\n",
                "import warnings\n",
                "warnings.filterwarnings('ignore')\n",
                "\n",
                "# Set up plotting style\n",
                "plt.style.use('default')\n",
                "sns.set_palette(\"husl\")\n",
                "plt.rcParams['figure.figsize'] = (15, 8)\n",
                "plt.rcParams['font.size'] = 10\n",
                "\n",
                "print(\"Libraries loaded successfully!\")"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Configuration\n",
                "DATASET_PATH = \"../../dataset_creation/squad_scheming_dataset.jsonl\"\n",
                "MODEL_NAME = \"meta-llama/Llama-3.1-8B\"\n",
                "\n",
                "# Load model and tokenizer\n",
                "print(f\"Loading model: {MODEL_NAME}\")\n",
                "tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)\n",
                "model = AutoModelForCausalLM.from_pretrained(\n",
                "    MODEL_NAME,\n",
                "    output_attentions=True,\n",
                "    attn_implementation=\"eager\",  # Required for attention extraction\n",
                "    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,\n",
                "    device_map=\"auto\" if torch.cuda.is_available() else None\n",
                ")\n",
                "\n",
                "# Add padding token if not present\n",
                "if tokenizer.pad_token is None:\n",
                "    tokenizer.pad_token = tokenizer.eos_token\n",
                "\n",
                "device = next(model.parameters()).device\n",
                "print(f\"Model loaded on device: {device}\")\n",
                "print(f\"Model has {model.config.num_hidden_layers} layers and {model.config.num_attention_heads} attention heads\")"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Load dataset\n",
                "def load_dataset(path):\n",
                "    data = []\n",
                "    with open(path, 'r', encoding='utf-8') as f:\n",
                "        for line in f:\n",
                "            data.append(json.loads(line.strip()))\n",
                "    return data\n",
                "\n",
                "dataset = load_dataset(DATASET_PATH)\n",
                "print(f\"Loaded {len(dataset)} examples from dataset\")\n",
                "\n",
                "# Display first example structure\n",
                "print(\"\\nExample structure:\")\n",
                "example = dataset[0]\n",
                "for key, value in example.items():\n",
                "    if isinstance(value, str) and len(value) > 100:\n",
                "        print(f\"{key}: {value[:100]}...\")\n",
                "    else:\n",
                "        print(f\"{key}: {value}\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Generation Attention Extraction Functions"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "def prepare_inputs_with_chat_template(system_prompt, user_prompt, max_length=512):\n",
                "    \"\"\"Prepare inputs for the model using chat template.\"\"\"\n",
                "    # Create conversation in chat format\n",
                "    messages = [\n",
                "        {\"role\": \"system\", \"content\": system_prompt},\n",
                "        {\"role\": \"user\", \"content\": user_prompt}\n",
                "    ]\n",
                "    \n",
                "    # Apply chat template\n",
                "    if hasattr(tokenizer, 'apply_chat_template') and tokenizer.chat_template is not None:\n",
                "        formatted_prompt = tokenizer.apply_chat_template(\n",
                "            messages, \n",
                "            tokenize=False, \n",
                "            add_generation_prompt=True\n",
                "        )\n",
                "    else:\n",
                "        # Fallback to manual formatting if no chat template\n",
                "        formatted_prompt = f\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\"\n",
                "    \n",
                "    # Tokenize\n",
                "    inputs = tokenizer(\n",
                "        formatted_prompt,\n",
                "        return_tensors=\"pt\",\n",
                "        max_length=max_length,\n",
                "        truncation=True,\n",
                "        padding=True\n",
                "    )\n",
                "    \n",
                "    return inputs, formatted_prompt\n",
                "\n",
                "def extract_generation_attention(inputs, layer_idx=-1, max_new_tokens=10):\n",
                "    \"\"\"Extract attention patterns during text generation.\"\"\"\n",
                "    model.eval()\n",
                "    \n",
                "    generation_data = {\n",
                "        'input_tokens': [],\n",
                "        'generated_tokens': [],\n",
                "        'generation_attentions': [],\n",
                "        'input_length': 0,\n",
                "        'full_sequence_tokens': []\n",
                "    }\n",
                "    \n",
                "    with torch.no_grad():\n",
                "        # Move inputs to device\n",
                "        input_ids = inputs['input_ids'].to(device)\n",
                "        attention_mask = inputs['attention_mask'].to(device)\n",
                "        \n",
                "        # Store input information\n",
                "        input_tokens = tokenizer.convert_ids_to_tokens(input_ids[0])\n",
                "        generation_data['input_tokens'] = input_tokens\n",
                "        generation_data['input_length'] = len(input_tokens)\n",
                "        \n",
                "        # Generate tokens one by one to capture attention at each step\n",
                "        current_ids = input_ids.clone()\n",
                "        current_mask = attention_mask.clone()\n",
                "        \n",
                "        for step in range(max_new_tokens):\n",
                "            # Get model output with attention\n",
                "            outputs = model(\n",
                "                input_ids=current_ids,\n",
                "                attention_mask=current_mask,\n",
                "                output_attentions=True,\n",
                "                use_cache=False  # Disable cache for consistent attention extraction\n",
                "            )\n",
                "            \n",
                "            # Get next token\n",
                "            logits = outputs.logits[0, -1, :]  # Last token logits\n",
                "            next_token_id = torch.argmax(logits, dim=-1).unsqueeze(0).unsqueeze(0)\n",
                "            next_token = tokenizer.convert_ids_to_tokens([next_token_id.item()])[0]\n",
                "            \n",
                "            # Store generated token\n",
                "            generation_data['generated_tokens'].append(next_token)\n",
                "            \n",
                "            # Extract attention from the last position (newly generated token)\n",
                "            # This shows what the generated token is attending to\n",
                "            step_attention = outputs.attentions[layer_idx][0]  # [heads, seq_len, seq_len]\n",
                "            last_token_attention = step_attention[:, -1, :]  # [heads, seq_len] - attention FROM last token TO all tokens\n",
                "            \n",
                "            generation_data['generation_attentions'].append(last_token_attention.cpu().numpy())\n",
                "            \n",
                "            # Store full sequence tokens at this step\n",
                "            full_tokens = tokenizer.convert_ids_to_tokens(current_ids[0]) + [next_token]\n",
                "            generation_data['full_sequence_tokens'].append(full_tokens.copy())\n",
                "            \n",
                "            # Update for next iteration\n",
                "            current_ids = torch.cat([current_ids, next_token_id], dim=1)\n",
                "            current_mask = torch.cat([current_mask, torch.ones(1, 1, device=device)], dim=1)\n",
                "            \n",
                "            # Stop if we hit EOS token\n",
                "            if next_token_id.item() == tokenizer.eos_token_id:\n",
                "                break\n",
                "        \n",
                "        return generation_data\n",
                "\n",
                "print(\"Generation attention extraction functions defined!\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Analysis and Visualization Functions"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "def analyze_generation_attention(generation_data, head_idx=0):\n",
                "    \"\"\"Analyze attention patterns from generated tokens to input tokens.\"\"\"\n",
                "    \n",
                "    if not generation_data['generated_tokens']:\n",
                "        print(\"No generated tokens found.\")\n",
                "        return None\n",
                "    \n",
                "    input_length = generation_data['input_length']\n",
                "    input_tokens = generation_data['input_tokens']\n",
                "    generated_tokens = generation_data['generated_tokens']\n",
                "    \n",
                "    print(f\"\\n🎯 GENERATION ATTENTION ANALYSIS (Head {head_idx})\")\n",
                "    print(f\"Input length: {input_length} tokens\")\n",
                "    print(f\"Generated: {len(generated_tokens)} tokens\")\n",
                "    \n",
                "    # Convert generated tokens to readable text\n",
                "    generated_text = tokenizer.convert_tokens_to_string(generated_tokens)\n",
                "    print(f\"Generated text: \\\"{generated_text}\\\"\")\n",
                "    \n",
                "    # Analyze each generated token's attention to input\n",
                "    token_analyses = []\n",
                "    \n",
                "    for step, (gen_token, attention_weights) in enumerate(zip(generated_tokens, generation_data['generation_attentions'])):\n",
                "        # Get attention from this generated token to input tokens\n",
                "        head_attention = attention_weights[head_idx]  # [seq_len]\n",
                "        input_attention = head_attention[:input_length]  # Only attention to input tokens\n",
                "        \n",
                "        # Find top attended input tokens\n",
                "        top_indices = np.argsort(input_attention)[-5:][::-1]  # Top 5\n",
                "        \n",
                "        analysis = {\n",
                "            'step': step,\n",
                "            'generated_token': gen_token,\n",
                "            'generated_text': tokenizer.convert_tokens_to_string([gen_token]),\n",
                "            'input_attention': input_attention,\n",
                "            'top_attended_tokens': []\n",
                "        }\n",
                "        \n",
                "        for idx in top_indices:\n",
                "            if idx < len(input_tokens):\n",
                "                analysis['top_attended_tokens'].append({\n",
                "                    'token': input_tokens[idx],\n",
                "                    'text': tokenizer.convert_tokens_to_string([input_tokens[idx]]),\n",
                "                    'attention': input_attention[idx],\n",
                "                    'position': idx\n",
                "                })\n",
                "        \n",
                "        token_analyses.append(analysis)\n",
                "        \n",
                "        # Print analysis for this token\n",
                "        print(f\"\\n📍 Step {step + 1}: Generated '{analysis['generated_text']}'\")\n",
                "        print(f\"   Top attended input tokens:\")\n",
                "        for i, token_info in enumerate(analysis['top_attended_tokens']):\n",
                "            print(f\"   {i+1}. '{token_info['text']}' (pos {token_info['position']}) - {token_info['attention']:.4f}\")\n",
                "    \n",
                "    return token_analyses\n",
                "\n",
                "print(\"Analysis functions defined!\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Visualization Functions"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "def create_generation_attention_heatmap(generation_data, head_idx=0, title=\"Generation Attention Heatmap\"):\n",
                "    \"\"\"Create heatmap showing attention from generated tokens to input tokens.\"\"\"\n",
                "    \n",
                "    if not generation_data['generated_tokens']:\n",
                "        print(\"No generation data available.\")\n",
                "        return None\n",
                "    \n",
                "    input_length = generation_data['input_length']\n",
                "    input_tokens = generation_data['input_tokens']\n",
                "    generated_tokens = generation_data['generated_tokens']\n",
                "    \n",
                "    # Build attention matrix: [generated_tokens, input_tokens]\n",
                "    attention_matrix = []\n",
                "    \n",
                "    for attention_weights in generation_data['generation_attentions']:\n",
                "        head_attention = attention_weights[head_idx]  # [seq_len]\n",
                "        input_attention = head_attention[:input_length]  # Only attention to input tokens\n",
                "        attention_matrix.append(input_attention)\n",
                "    \n",
                "    attention_matrix = np.array(attention_matrix)  # [num_generated, input_length]\n",
                "    \n",
                "    # Clean tokens for display (NO TRUNCATION)\n",
                "    clean_input_tokens = []\n",
                "    for token in input_tokens:\n",
                "        clean_token = token.replace('▁', ' ').replace('Ġ', ' ')\n",
                "        if clean_token.startswith(' '):\n",
                "            clean_token = clean_token[1:]\n",
                "        clean_input_tokens.append(clean_token)  # Full token, no truncation\n",
                "    \n",
                "    clean_generated_tokens = []\n",
                "    for token in generated_tokens:\n",
                "        clean_token = token.replace('▁', ' ').replace('Ġ', ' ')\n",
                "        if clean_token.startswith(' '):\n",
                "            clean_token = clean_token[1:]\n",
                "        clean_generated_tokens.append(clean_token)  # Full token, no truncation\n",
                "    \n",
                "    # Create interactive heatmap\n",
                "    fig = go.Figure(data=go.Heatmap(\n",
                "        z=attention_matrix,\n",
                "        x=clean_input_tokens,\n",
                "        y=[f\"Gen{i+1}: {token}\" for i, token in enumerate(clean_generated_tokens)],\n",
                "        colorscale='Reds',\n",
                "        hoverongaps=False,\n",
                "        hovertemplate='<b>Generated:</b> %{y}<br><b>Input Token:</b> %{x}<br><b>Attention:</b> %{z:.4f}<extra></extra>'\n",
                "    ))\n",
                "    \n",
                "    fig.update_layout(\n",
                "        title=f\"{title} (Head {head_idx})\",\n",
                "        xaxis_title=\"Input Tokens (Full Text)\",\n",
                "        yaxis_title=\"Generated Tokens\",\n",
                "        width=max(1200, len(clean_input_tokens) * 15),  # Dynamic width based on input length\n",
                "        height=400 + len(generated_tokens) * 30,\n",
                "        xaxis_tickangle=-45\n",
                "    )\n",
                "    \n",
                "    return fig\n",
                "\n",
                "def create_colored_input_visualization(generation_data, target_gen_step=0, head_idx=0):\n",
                "    \"\"\"Create colored visualization of input tokens based on attention from a specific generated token.\"\"\"\n",
                "    \n",
                "    if target_gen_step >= len(generation_data['generated_tokens']):\n",
                "        print(f\"Invalid generation step. Only {len(generation_data['generated_tokens'])} tokens generated.\")\n",
                "        return None\n",
                "    \n",
                "    input_tokens = generation_data['input_tokens']\n",
                "    generated_token = generation_data['generated_tokens'][target_gen_step]\n",
                "    attention_weights = generation_data['generation_attentions'][target_gen_step]\n",
                "    \n",
                "    # Get attention from the generated token to input tokens\n",
                "    head_attention = attention_weights[head_idx]\n",
                "    input_attention = head_attention[:len(input_tokens)]\n",
                "    \n",
                "    # Normalize attention weights to [0, 1]\n",
                "    if len(input_attention) > 0:\n",
                "        min_att = np.min(input_attention)\n",
                "        max_att = np.max(input_attention)\n",
                "        if max_att > min_att:\n",
                "            normalized_weights = (input_attention - min_att) / (max_att - min_att)\n",
                "        else:\n",
                "            normalized_weights = np.ones_like(input_attention) * 0.5\n",
                "    else:\n",
                "        normalized_weights = np.array([])\n",
                "    \n",
                "    # Create colormap\n",
                "    cmap = plt.cm.get_cmap('Reds')\n",
                "    \n",
                "    # Generate HTML\n",
                "    generated_text = tokenizer.convert_tokens_to_string([generated_token])\n",
                "    html_parts = []\n",
                "    html_parts.append(f'<div style=\"font-family: monospace; font-size: 14px; line-height: 1.8; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #fafafa; max-width: 100%; overflow-wrap: break-word;\">')\n",
                "    html_parts.append(f'<h3 style=\"margin-top: 0; color: #333;\">Attention from Generated Token \\\"{generated_text}\\\" to Input (Head {head_idx})</h3>')\n",
                "    html_parts.append(f'<div style=\"margin-bottom: 15px; font-weight: bold;\">Full Input Text with Attention Coloring:</div>')\n",
                "    \n",
                "    for i, token in enumerate(input_tokens):\n",
                "        if i < len(normalized_weights):\n",
                "            # Get color from colormap\n",
                "            rgba = cmap(normalized_weights[i])\n",
                "            color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'\n",
                "            \n",
                "            # Clean token for display (NO TRUNCATION)\n",
                "            display_token = token.replace('▁', ' ').replace('Ġ', ' ')\n",
                "            if display_token.startswith(' '):\n",
                "                display_token = display_token[1:]\n",
                "            \n",
                "            # Create colored span with tooltip\n",
                "            html_parts.append(\n",
                "                f'<span style=\"background-color: {color}; padding: 2px 4px; margin: 1px; '\n",
                "                f'border-radius: 3px; display: inline-block; word-break: break-all;\" '\n",
                "                f'title=\"Token: {display_token}\\\\nAttention: {input_attention[i]:.4f}\\\\nNormalized: {normalized_weights[i]:.4f}\\\\nPosition: {i}\">{display_token}</span>'\n",
                "            )\n",
                "        else:\n",
                "            # Token without attention weight\n",
                "            display_token = token.replace('▁', ' ').replace('Ġ', ' ')\n",
                "            if display_token.startswith(' '):\n",
                "                display_token = display_token[1:]\n",
                "            html_parts.append(f'<span style=\"padding: 2px 4px; margin: 1px; word-break: break-all;\">{display_token}</span>')\n",
                "    \n",
                "    # Add colorbar legend\n",
                "    html_parts.append('<div style=\"margin-top: 15px; font-size: 12px;\">')\n",
                "    html_parts.append('<strong>Attention Scale:</strong> ')\n",
                "    \n",
                "    # Create mini colorbar\n",
                "    for i in range(10):\n",
                "        intensity = i / 9.0\n",
                "        rgba = cmap(intensity)\n",
                "        color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'\n",
                "        html_parts.append(f'<span style=\"background-color: {color}; width: 20px; height: 15px; display: inline-block; margin: 1px;\"></span>')\n",
                "    \n",
                "    if len(input_attention) > 0:\n",
                "        html_parts.append(f'<br><span style=\"font-size: 10px;\">Min: {min_att:.4f} | Max: {max_att:.4f}</span>')\n",
                "    \n",
                "    html_parts.append('</div>')\n",
                "    html_parts.append('</div>')\n",
                "    \n",
                "    return ''.join(html_parts)\n",
                "\n",
                "def create_full_scenario_visualization(results, target_gen_step=0):\n",
                "    \"\"\"Create comprehensive visualization showing both scenarios side by side.\"\"\"\n",
                "    \n",
                "    print(f\"\\n{'='*100}\")\n",
                "    print(f\"COMPREHENSIVE ATTENTION VISUALIZATION - GENERATION STEP {target_gen_step + 1}\")\n",
                "    print(f\"{'='*100}\")\n",
                "    \n",
                "    # Display full prompts without truncation\n",
                "    print(f\"\\n🔴 SCHEMING SCENARIO FULL PROMPTS:\")\n",
                "    print(f\"\\n🤖 SYSTEM PROMPT:\")\n",
                "    print(f\"{results['scheming']['system']}\")\n",
                "    print(f\"\\n👤 USER PROMPT:\")\n",
                "    print(f\"{results['scheming']['user']}\")\n",
                "    \n",
                "    print(f\"\\n🔵 BASELINE SCENARIO FULL PROMPTS:\")\n",
                "    print(f\"\\n🤖 SYSTEM PROMPT:\")\n",
                "    print(f\"{results['baseline']['system']}\")\n",
                "    print(f\"\\n👤 USER PROMPT:\")\n",
                "    print(f\"{results['baseline']['user']}\")\n",
                "    \n",
                "    # Show generated responses\n",
                "    scheming_tokens = results['scheming']['generation_data']['generated_tokens']\n",
                "    baseline_tokens = results['baseline']['generation_data']['generated_tokens']\n",
                "    \n",
                "    if scheming_tokens:\n",
                "        scheming_response = tokenizer.convert_tokens_to_string(scheming_tokens)\n",
                "        print(f\"\\n🔴 SCHEMING RESPONSE: \\\"{scheming_response}\\\"\")\n",
                "    \n",
                "    if baseline_tokens:\n",
                "        baseline_response = tokenizer.convert_tokens_to_string(baseline_tokens)\n",
                "        print(f\"🔵 BASELINE RESPONSE: \\\"{baseline_response}\\\"\")\n",
                "    \n",
                "    # Create colored visualizations\n",
                "    if target_gen_step < len(scheming_tokens):\n",
                "        scheming_html = create_colored_input_visualization(\n",
                "            results['scheming']['generation_data'], \n",
                "            target_gen_step, \n",
                "            results['head_idx']\n",
                "        )\n",
                "        print(f\"\\n📊 SCHEMING SCENARIO - COLORED INPUT VISUALIZATION:\")\n",
                "        display(HTML(scheming_html))\n",
                "    \n",
                "    if target_gen_step < len(baseline_tokens):\n",
                "        baseline_html = create_colored_input_visualization(\n",
                "            results['baseline']['generation_data'], \n",
                "            target_gen_step, \n",
                "            results['head_idx']\n",
                "        )\n",
                "        print(f\"\\n📊 BASELINE SCENARIO - COLORED INPUT VISUALIZATION:\")\n",
                "        display(HTML(baseline_html))\n",
                "    \n",
                "    # Create heatmaps\n",
                "    print(f\"\\n📈 INTERACTIVE ATTENTION HEATMAPS:\")\n",
                "    \n",
                "    scheming_heatmap = create_generation_attention_heatmap(\n",
                "        results['scheming']['generation_data'],\n",
                "        results['head_idx'],\n",
                "        \"🔴 Scheming Scenario - Generation Attention\"\n",
                "    )\n",
                "    if scheming_heatmap:\n",
                "        scheming_heatmap.show()\n",
                "    \n",
                "    baseline_heatmap = create_generation_attention_heatmap(\n",
                "        results['baseline']['generation_data'],\n",
                "        results['head_idx'],\n",
                "        \"🔵 Baseline Scenario - Generation Attention\"\n",
                "    )\n",
                "    if baseline_heatmap:\n",
                "        baseline_heatmap.show()\n",
                "\n",
                "print(\"Visualization functions defined!\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Scenario Setup and Analysis"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "def create_prompt_scenarios(example):\n",
                "    \"\"\"Create scheming and baseline prompt scenarios from dataset example.\"\"\"\n",
                "    \n",
                "    # Scenario A: Scheming (use pressure-inducing system prompt)\n",
                "    scheming_system = example['system_prompt']\n",
                "    scheming_user = example['user_prompt']\n",
                "    \n",
                "    # Scenario B: Baseline (neutral system prompt with original context)\n",
                "    baseline_system = \"You are a helpful assistant.\"\n",
                "    baseline_user = f\"Context: {example['original_context']}\\n\\nQuestion: {example['original_question']}\"\n",
                "    \n",
                "    return {\n",
                "        'scheming': {\n",
                "            'system': scheming_system,\n",
                "            'user': scheming_user\n",
                "        },\n",
                "        'baseline': {\n",
                "            'system': baseline_system,\n",
                "            'user': baseline_user\n",
                "        },\n",
                "        'metadata': {\n",
                "            'pressure_category': example.get('pressure_category', 'unknown'),\n",
                "            'pressure_source': example.get('pressure_source', 'unknown'),\n",
                "            'ground_truth': example['ground_truth'],\n",
                "            'honest_answer': example['honest'],\n",
                "            'dishonest_answer': example['dishonest']\n",
                "        }\n",
                "    }\n",
                "\n",
                "def analyze_example_generation_attention(example_idx, layer_idx=-1, head_idx=0, max_tokens=10):\n",
                "    \"\"\"Analyze generation attention for both scenarios of a given example.\"\"\"\n",
                "    \n",
                "    if example_idx >= len(dataset):\n",
                "        print(f\"Invalid example index. Dataset has {len(dataset)} examples.\")\n",
                "        return None\n",
                "    \n",
                "    example = dataset[example_idx]\n",
                "    scenarios = create_prompt_scenarios(example)\n",
                "    \n",
                "    print(f\"\\n{'='*80}\")\n",
                "    print(f\"GENERATION ATTENTION ANALYSIS - EXAMPLE {example_idx}\")\n",
                "    print(f\"{'='*80}\")\n",
                "    print(f\"Pressure Category: {scenarios['metadata']['pressure_category']}\")\n",
                "    print(f\"Pressure Source: {scenarios['metadata']['pressure_source']}\")\n",
                "    print(f\"Ground Truth: {scenarios['metadata']['ground_truth']}\")\n",
                "    print(f\"Expected Honest: {scenarios['metadata']['honest_answer']}\")\n",
                "    print(f\"Expected Dishonest: {scenarios['metadata']['dishonest_answer']}\")\n",
                "    print(f\"Layer: {layer_idx} | Head: {head_idx}\")\n",
                "    \n",
                "    results = {}\n",
                "    \n",
                "    # Analyze scheming scenario\n",
                "    print(f\"\\n🔴 ANALYZING SCHEMING SCENARIO...\")\n",
                "    print(f\"System: {scenarios['scheming']['system']}\")\n",
                "    print(f\"User: {scenarios['scheming']['user']}\")\n",
                "    \n",
                "    scheming_inputs, scheming_prompt = prepare_inputs_with_chat_template(\n",
                "        scenarios['scheming']['system'], \n",
                "        scenarios['scheming']['user']\n",
                "    )\n",
                "    \n",
                "    scheming_generation_data = extract_generation_attention(\n",
                "        scheming_inputs, layer_idx, max_tokens\n",
                "    )\n",
                "    \n",
                "    scheming_analysis = analyze_generation_attention(scheming_generation_data, head_idx)\n",
                "    \n",
                "    # Analyze baseline scenario\n",
                "    print(f\"\\n🔵 ANALYZING BASELINE SCENARIO...\")\n",
                "    print(f\"System: {scenarios['baseline']['system']}\")\n",
                "    print(f\"User: {scenarios['baseline']['user']}\")\n",
                "    \n",
                "    baseline_inputs, baseline_prompt = prepare_inputs_with_chat_template(\n",
                "        scenarios['baseline']['system'], \n",
                "        scenarios['baseline']['user']\n",
                "    )\n",
                "    \n",
                "    baseline_generation_data = extract_generation_attention(\n",
                "        baseline_inputs, layer_idx, max_tokens\n",
                "    )\n",
                "    \n",
                "    baseline_analysis = analyze_generation_attention(baseline_generation_data, head_idx)\n",
                "    \n",
                "    results = {\n",
                "        'scheming': {\n",
                "            'generation_data': scheming_generation_data,\n",
                "            'analysis': scheming_analysis,\n",
                "            'system': scenarios['scheming']['system'],\n",
                "            'user': scenarios['scheming']['user']\n",
                "        },\n",
                "        'baseline': {\n",
                "            'generation_data': baseline_generation_data,\n",
                "            'analysis': baseline_analysis,\n",
                "            'system': scenarios['baseline']['system'],\n",
                "            'user': scenarios['baseline']['user']\n",
                "        },\n",
                "        'metadata': scenarios['metadata'],\n",
                "        'layer_idx': layer_idx,\n",
                "        'head_idx': head_idx,\n",
                "        'example_idx': example_idx\n",
                "    }\n",
                "    \n",
                "    return results\n",
                "\n",
                "print(\"Scenario analysis functions defined!\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Interactive Analysis Interface"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Create interactive widgets for analysis\n",
                "example_slider = widgets.IntSlider(\n",
                "    value=0,\n",
                "    min=0,\n",
                "    max=min(len(dataset)-1, 10),  # Limit to first 10 examples for performance\n",
                "    step=1,\n",
                "    description='Example:',\n",
                "    style={'description_width': 'initial'}\n",
                ")\n",
                "\n",
                "layer_slider = widgets.IntSlider(\n",
                "    value=-1,\n",
                "    min=-1,\n",
                "    max=31,  # Llama-3.1-8B has 32 layers (0-31), -1 for last layer\n",
                "    step=1,\n",
                "    description='Layer:',\n",
                "    style={'description_width': 'initial'}\n",
                ")\n",
                "\n",
                "head_slider = widgets.IntSlider(\n",
                "    value=0,\n",
                "    min=0,\n",
                "    max=31,  # Llama-3.1-8B has 32 attention heads (0-31)\n",
                "    step=1,\n",
                "    description='Head:',\n",
                "    style={'description_width': 'initial'}\n",
                ")\n",
                "\n",
                "max_tokens_slider = widgets.IntSlider(\n",
                "    value=5,\n",
                "    min=1,\n",
                "    max=20,\n",
                "    step=1,\n",
                "    description='Max Tokens:',\n",
                "    style={'description_width': 'initial'}\n",
                ")\n",
                "\n",
                "# Display widgets\n",
                "display(widgets.VBox([\n",
                "    widgets.HTML('<h3>🎛️ Generation Attention Analysis Controls</h3>'),\n",
                "    example_slider,\n",
                "    widgets.HBox([layer_slider, head_slider]),\n",
                "    max_tokens_slider\n",
                "]))\n",
                "\n",
                "print(\"Interactive widgets created! Use the controls above to select examples and parameters.\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Run Generation Attention Analysis"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Run analysis on currently selected example\n",
                "results = analyze_example_generation_attention(\n",
                "    example_slider.value, \n",
                "    layer_slider.value, \n",
                "    head_slider.value,\n",
                "    max_tokens_slider.value\n",
                ")\n",
                "\n",
                "if results:\n",
                "    print(\"\\n✅ Generation attention analysis complete!\")\n",
                "    \n",
                "    # Analyze the generated responses\n",
                "    if (results['scheming']['generation_data']['generated_tokens'] and \n",
                "        results['baseline']['generation_data']['generated_tokens']):\n",
                "        \n",
                "        scheming_response = tokenizer.convert_tokens_to_string(results['scheming']['generation_data']['generated_tokens'])\n",
                "        baseline_response = tokenizer.convert_tokens_to_string(results['baseline']['generation_data']['generated_tokens'])\n",
                "        \n",
                "        print(f\"\\n📝 RESPONSE ANALYSIS:\")\n",
                "        print(f\"🔴 Scheming response: '{scheming_response}'\")\n",
                "        print(f\"🔵 Baseline response: '{baseline_response}'\")\n",
                "        \n",
                "        # Check if the model is giving a generic response\n",
                "        if 'You are' in scheming_response or 'I am' in scheming_response:\n",
                "            print(f\"\\n⚠️  ANALYSIS: The model appears to be giving a generic/safe response in the scheming scenario.\")\n",
                "            print(f\"    This often happens when models encounter conflicting instructions or pressure.\")\n",
                "            print(f\"    The model may be falling back to repeating parts of the system prompt rather than\")\n",
                "            print(f\"    directly answering the question. This is actually a form of resistance to pressure!\")\n",
                "        \n",
                "        # Check if responses contain the expected answers\n",
                "        honest_answer = results['metadata']['honest_answer']\n",
                "        dishonest_answer = results['metadata']['dishonest_answer']\n",
                "        \n",
                "        if honest_answer.lower() in baseline_response.lower():\n",
                "            print(f\"\\n✓ Baseline contains expected honest answer: '{honest_answer}'\")\n",
                "        \n",
                "        if dishonest_answer.lower() in scheming_response.lower():\n",
                "            print(f\"\\n⚠️  Scheming contains expected dishonest answer: '{dishonest_answer}'\")\n",
                "        elif honest_answer.lower() in scheming_response.lower():\n",
                "            print(f\"\\n✓ Scheming actually contains honest answer: '{honest_answer}' (pressure failed!)\")\n",
                "        else:\n",
                "            print(f\"\\n🤔 Scheming response doesn't contain expected answer - model may be deflecting\")\n",
                "    \n",
                "    print(\"\\n📊 Scroll up to see detailed attention analysis for each generated token.\")\n",
                "else:\n",
                "    print(\"❌ Analysis failed. Please check the example index and try again.\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Comprehensive Visualization with Colored Tokens and Heatmaps"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Create comprehensive visualization if we have results\n",
                "if 'results' in locals() and results:\n",
                "    print(\"Creating comprehensive attention visualizations...\")\n",
                "    \n",
                "    # Show visualization for the first generated token (most important)\n",
                "    create_full_scenario_visualization(results, target_gen_step=0)\n",
                "    \n",
                "    print(\"\\n\" + \"=\"*100)\n",
                "    print(\"VISUALIZATION COMPLETE!\")\n",
                "    print(\"=\"*100)\n",
                "    print(\"\\n🎯 What you're seeing:\")\n",
                "    print(\"1. 📝 Full prompts and responses (no truncation)\")\n",
                "    print(\"2. 🎨 Colored input text showing attention intensity\")\n",
                "    print(\"3. 📊 Interactive heatmaps showing attention patterns\")\n",
                "    print(\"4. 🔍 Hover over tokens and heatmap cells for detailed values\")\n",
                "    \n",
                "    print(\"\\n💡 Key insights to look for:\")\n",
                "    print(\"- Darker red colors = Higher attention\")\n",
                "    print(\"- Compare which words get attention in scheming vs baseline\")\n",
                "    print(\"- Look for attention to authority words vs factual content\")\n",
                "    print(\"- Check if pressure words drive the model's response\")\n",
                "    \n",
                "else:\n",
                "    print(\"❌ No analysis results available. Please run the generation analysis first.\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Interactive Token-by-Token Analysis"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Widget to select which generated token to analyze\n",
                "if 'results' in locals() and results:\n",
                "    max_tokens_generated = max(\n",
                "        len(results['scheming']['generation_data']['generated_tokens']),\n",
                "        len(results['baseline']['generation_data']['generated_tokens'])\n",
                "    )\n",
                "    \n",
                "    if max_tokens_generated > 0:\n",
                "        token_step_slider = widgets.IntSlider(\n",
                "            value=0,\n",
                "            min=0,\n",
                "            max=max_tokens_generated - 1,\n",
                "            step=1,\n",
                "            description='Token Step:',\n",
                "            style={'description_width': 'initial'}\n",
                "        )\n",
                "        \n",
                "        display(widgets.VBox([\n",
                "            widgets.HTML('<h3>🔍 Select Generated Token to Analyze</h3>'),\n",
                "            token_step_slider,\n",
                "            widgets.HTML('<p>Use the slider to see attention patterns for different generated tokens.</p>')\n",
                "        ]))\n",
                "        \n",
                "        print(\"Use the slider above to explore attention for different generated tokens.\")\n",
                "    else:\n",
                "        print(\"No tokens were generated to analyze.\")\n",
                "else:\n",
                "    print(\"Run the analysis first to enable token-by-token exploration.\")"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Run visualization for selected token step\n",
                "if 'results' in locals() and results and 'token_step_slider' in locals():\n",
                "    create_full_scenario_visualization(results, target_gen_step=token_step_slider.value)\n",
                "else:\n",
                "    print(\"Please run the analysis and use the token step slider above.\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Average Attention Across All Generated Tokens"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "def calculate_average_generation_attention(generation_data, head_idx=0):\n",
                "    \"\"\"Calculate average attention across all generated tokens.\"\"\"\n",
                "    \n",
                "    if not generation_data['generated_tokens']:\n",
                "        print(\"No generation data available.\")\n",
                "        return None\n",
                "    \n",
                "    input_length = generation_data['input_length']\n",
                "    input_tokens = generation_data['input_tokens']\n",
                "    generated_tokens = generation_data['generated_tokens']\n",
                "    \n",
                "    # Collect attention weights for all generated tokens\n",
                "    all_attention_weights = []\n",
                "    \n",
                "    for attention_weights in generation_data['generation_attentions']:\n",
                "        head_attention = attention_weights[head_idx]  # [seq_len]\n",
                "        input_attention = head_attention[:input_length]  # Only attention to input tokens\n",
                "        all_attention_weights.append(input_attention)\n",
                "    \n",
                "    # Calculate average attention across all generated tokens\n",
                "    if all_attention_weights:\n",
                "        average_attention = np.mean(all_attention_weights, axis=0)  # [input_length]\n",
                "        \n",
                "        # Find top attended input tokens\n",
                "        top_indices = np.argsort(average_attention)[-10:][::-1]  # Top 10\n",
                "        \n",
                "        analysis = {\n",
                "            'average_attention': average_attention,\n",
                "            'input_tokens': input_tokens,\n",
                "            'generated_tokens': generated_tokens,\n",
                "            'top_attended_tokens': []\n",
                "        }\n",
                "        \n",
                "        for idx in top_indices:\n",
                "            if idx < len(input_tokens):\n",
                "                analysis['top_attended_tokens'].append({\n",
                "                    'token': input_tokens[idx],\n",
                "                    'text': tokenizer.convert_tokens_to_string([input_tokens[idx]]),\n",
                "                    'attention': average_attention[idx],\n",
                "                    'position': idx\n",
                "                })\n",
                "        \n",
                "        return analysis\n",
                "    \n",
                "    return None\n",
                "\n",
                "def create_average_attention_visualization(generation_data, head_idx=0, title=\"Average Attention Across All Generated Tokens\"):\n",
                "    \"\"\"Create colored visualization showing average attention across all generated tokens.\"\"\"\n",
                "    \n",
                "    analysis = calculate_average_generation_attention(generation_data, head_idx)\n",
                "    \n",
                "    if not analysis:\n",
                "        print(\"No analysis data available.\")\n",
                "        return None\n",
                "    \n",
                "    input_tokens = analysis['input_tokens']\n",
                "    average_attention = analysis['average_attention']\n",
                "    generated_tokens = analysis['generated_tokens']\n",
                "    \n",
                "    # Normalize attention weights to [0, 1]\n",
                "    if len(average_attention) > 0:\n",
                "        min_att = np.min(average_attention)\n",
                "        max_att = np.max(average_attention)\n",
                "        if max_att > min_att:\n",
                "            normalized_weights = (average_attention - min_att) / (max_att - min_att)\n",
                "        else:\n",
                "            normalized_weights = np.ones_like(average_attention) * 0.5\n",
                "    else:\n",
                "        normalized_weights = np.array([])\n",
                "    \n",
                "    # Create colormap\n",
                "    cmap = plt.cm.get_cmap('Purples')  # Use purple to distinguish from single-token analysis\n",
                "    \n",
                "    # Generate HTML\n",
                "    generated_text = tokenizer.convert_tokens_to_string(generated_tokens)\n",
                "    html_parts = []\n",
                "    html_parts.append(f'<div style=\"font-family: monospace; font-size: 14px; line-height: 1.8; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #fafafa; max-width: 100%; overflow-wrap: break-word;\">')\n",
                "    html_parts.append(f'<h3 style=\"margin-top: 0; color: #333;\">{title} (Head {head_idx})</h3>')\n",
                "    html_parts.append(f'<div style=\"margin-bottom: 10px; font-weight: bold;\">Generated Response: \\\"{generated_text}\\\"</div>')\n",
                "    html_parts.append(f'<div style=\"margin-bottom: 15px; font-weight: bold;\">Average Attention Across {len(generated_tokens)} Generated Tokens:</div>')\n",
                "    \n",
                "    for i, token in enumerate(input_tokens):\n",
                "        if i < len(normalized_weights):\n",
                "            # Get color from colormap\n",
                "            rgba = cmap(normalized_weights[i])\n",
                "            color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'\n",
                "            \n",
                "            # Clean token for display\n",
                "            display_token = token.replace('▁', ' ').replace('Ġ', ' ')\n",
                "            if display_token.startswith(' '):\n",
                "                display_token = display_token[1:]\n",
                "            \n",
                "            # Create colored span with tooltip\n",
                "            html_parts.append(\n",
                "                f'<span style=\"background-color: {color}; padding: 2px 4px; margin: 1px; '\n",
                "                f'border-radius: 3px; display: inline-block; word-break: break-all;\" '\n",
                "                f'title=\"Token: {display_token}\\\\nAverage Attention: {average_attention[i]:.4f}\\\\nNormalized: {normalized_weights[i]:.4f}\\\\nPosition: {i}\">{display_token}</span>'\n",
                "            )\n",
                "        else:\n",
                "            # Token without attention weight\n",
                "            display_token = token.replace('▁', ' ').replace('Ġ', ' ')\n",
                "            if display_token.startswith(' '):\n",
                "                display_token = display_token[1:]\n",
                "            html_parts.append(f'<span style=\"padding: 2px 4px; margin: 1px; word-break: break-all;\">{display_token}</span>')\n",
                "    \n",
                "    # Add colorbar legend\n",
                "    html_parts.append('<div style=\"margin-top: 15px; font-size: 12px;\">')\n",
                "    html_parts.append('<strong>Average Attention Scale:</strong> ')\n",
                "    \n",
                "    # Create mini colorbar\n",
                "    for i in range(10):\n",
                "        intensity = i / 9.0\n",
                "        rgba = cmap(intensity)\n",
                "        color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'\n",
                "        html_parts.append(f'<span style=\"background-color: {color}; width: 20px; height: 15px; display: inline-block; margin: 1px;\"></span>')\n",
                "    \n",
                "    if len(average_attention) > 0:\n",
                "        html_parts.append(f'<br><span style=\"font-size: 10px;\">Min: {min_att:.4f} | Max: {max_att:.4f}</span>')\n",
                "    \n",
                "    html_parts.append('</div>')\n",
                "    html_parts.append('</div>')\n",
                "    \n",
                "    return ''.join(html_parts), analysis\n",
                "\n",
                "def create_average_attention_comparison_plot(scheming_analysis, baseline_analysis, head_idx=0):\n",
                "    \"\"\"Create comparison plot of average attention patterns between scenarios.\"\"\"\n",
                "    \n",
                "    if not scheming_analysis or not baseline_analysis:\n",
                "        print(\"Missing analysis data for comparison.\")\n",
                "        return None\n",
                "    \n",
                "    # Get top tokens from both scenarios\n",
                "    scheming_top = scheming_analysis['top_attended_tokens'][:15]\n",
                "    baseline_top = baseline_analysis['top_attended_tokens'][:15]\n",
                "    \n",
                "    # Create comparison plot\n",
                "    fig = make_subplots(\n",
                "        rows=1, cols=2,\n",
                "        subplot_titles=[\"🔴 Scheming - Top Average Attention\", \"🔵 Baseline - Top Average Attention\"],\n",
                "        specs=[[{\"type\": \"bar\"}, {\"type\": \"bar\"}]]\n",
                "    )\n",
                "    \n",
                "    # Scheming scenario\n",
                "    scheming_tokens = [item['text'][:15] for item in scheming_top]\n",
                "    scheming_attentions = [item['attention'] for item in scheming_top]\n",
                "    \n",
                "    fig.add_trace(\n",
                "        go.Bar(\n",
                "            x=scheming_tokens,\n",
                "            y=scheming_attentions,\n",
                "            name=\"Scheming\",\n",
                "            marker_color='red',\n",
                "            hovertemplate='<b>Token:</b> %{x}<br><b>Avg Attention:</b> %{y:.4f}<extra></extra>'\n",
                "        ),\n",
                "        row=1, col=1\n",
                "    )\n",
                "    \n",
                "    # Baseline scenario\n",
                "    baseline_tokens = [item['text'][:15] for item in baseline_top]\n",
                "    baseline_attentions = [item['attention'] for item in baseline_top]\n",
                "    \n",
                "    fig.add_trace(\n",
                "        go.Bar(\n",
                "            x=baseline_tokens,\n",
                "            y=baseline_attentions,\n",
                "            name=\"Baseline\",\n",
                "            marker_color='blue',\n",
                "            hovertemplate='<b>Token:</b> %{x}<br><b>Avg Attention:</b> %{y:.4f}<extra></extra>'\n",
                "        ),\n",
                "        row=1, col=2\n",
                "    )\n",
                "    \n",
                "    fig.update_layout(\n",
                "        title=f\"Average Attention Comparison Across All Generated Tokens (Head {head_idx})\",\n",
                "        showlegend=False,\n",
                "        height=500\n",
                "    )\n",
                "    \n",
                "    fig.update_xaxes(tickangle=-45)\n",
                "    fig.update_yaxes(title_text=\"Average Attention\")\n",
                "    \n",
                "    return fig\n",
                "\n",
                "print(\"Average attention analysis functions defined!\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Run Average Attention Analysis"
            ]
        },
        {
            "cell_type": "code",
            "execution_count": null,
            "metadata": {},
            "outputs": [],
            "source": [
                "# Analyze average attention across all generated tokens\n",
                "if 'results' in locals() and results:\n",
                "    print(\"\\n\" + \"=\"*100)\n",
                "    print(\"AVERAGE ATTENTION ANALYSIS ACROSS ALL GENERATED TOKENS\")\n",
                "    print(\"=\"*100)\n",
                "    \n",
                "    # Calculate average attention for both scenarios\n",
                "    scheming_avg_analysis = calculate_average_generation_attention(\n",
                "        results['scheming']['generation_data'], \n",
                "        results['head_idx']\n",
                "    )\n",
                "    \n",
                "    baseline_avg_analysis = calculate_average_generation_attention(\n",
                "        results['baseline']['generation_data'], \n",
                "        results['head_idx']\n",
                "    )\n",
                "    \n",
                "    if scheming_avg_analysis and baseline_avg_analysis:\n",
                "        # Print top attended tokens for each scenario\n",
                "        print(f\"\\n🔴 SCHEMING SCENARIO - TOP AVERAGE ATTENTION:\")\n",
                "        scheming_response = tokenizer.convert_tokens_to_string(scheming_avg_analysis['generated_tokens'])\n",
                "        print(f\"Generated Response: \\\"{scheming_response}\\\"\")\n",
                "        print(f\"Top attended input tokens (averaged across all {len(scheming_avg_analysis['generated_tokens'])} generated tokens):\")\n",
                "        \n",
                "        for i, token_info in enumerate(scheming_avg_analysis['top_attended_tokens'][:10]):\n",
                "            print(f\"   {i+1:2d}. '{token_info['text']}' (pos {token_info['position']}) - {token_info['attention']:.4f}\")\n",
                "        \n",
                "        print(f\"\\n🔵 BASELINE SCENARIO - TOP AVERAGE ATTENTION:\")\n",
                "        baseline_response = tokenizer.convert_tokens_to_string(baseline_avg_analysis['generated_tokens'])\n",
                "        print(f\"Generated Response: \\\"{baseline_response}\\\"\")\n",
                "        print(f\"Top attended input tokens (averaged across all {len(baseline_avg_analysis['generated_tokens'])} generated tokens):\")\n",
                "        \n",
                "        for i, token_info in enumerate(baseline_avg_analysis['top_attended_tokens'][:10]):\n",
                "            print(f\"   {i+1:2d}. '{token_info['text']}' (pos {token_info['position']}) - {token_info['attention']:.4f}\")\n",
                "        \n",
                "        # Create colored visualizations\n",
                "        print(f\"\\n📊 AVERAGE ATTENTION VISUALIZATIONS:\")\n",
                "        \n",
                "        scheming_avg_html, _ = create_average_attention_visualization(\n",
                "            results['scheming']['generation_data'],\n",
                "            results['head_idx'],\n",
                "            \"🔴 Scheming - Average Attention Across All Generated Tokens\"\n",
                "        )\n",
                "        \n",
                "        if scheming_avg_html:\n",
                "            display(HTML(scheming_avg_html))\n",
                "        \n",
                "        baseline_avg_html, _ = create_average_attention_visualization(\n",
                "            results['baseline']['generation_data'],\n",
                "            results['head_idx'],\n",
                "            \"🔵 Baseline - Average Attention Across All Generated Tokens\"\n",
                "        )\n",
                "        \n",
                "        if baseline_avg_html:\n",
                "            display(HTML(baseline_avg_html))\n",
                "        \n",
                "        # Create comparison plot\n",
                "        print(f\"\\n📈 AVERAGE ATTENTION COMPARISON PLOT:\")\n",
                "        comparison_fig = create_average_attention_comparison_plot(\n",
                "            scheming_avg_analysis, \n",
                "            baseline_avg_analysis, \n",
                "            results['head_idx']\n",
                "        )\n",
                "        \n",
                "        if comparison_fig:\n",
                "            comparison_fig.show()\n",
                "        \n",
                "        print(f\"\\n✅ Average attention analysis complete!\")\n",
                "        print(f\"\\n💡 Key insights:\")\n",
                "        print(f\"- Purple coloring shows average attention across ALL generated tokens\")\n",
                "        print(f\"- Compare which input words get consistent attention throughout the response\")\n",
                "        print(f\"- Look for systematic differences between scheming and baseline scenarios\")\n",
                "        print(f\"- Darker purple = higher average attention across the entire response\")\n",
                "        \n",
                "    else:\n",
                "        print(\"❌ Could not calculate average attention. Check if tokens were generated.\")\n",
                "        \n",
                "else:\n",
                "    print(\"❌ No analysis results available. Please run the generation analysis first.\")"
            ]
        },
        {
            "cell_type": "markdown",
            "metadata": {},
            "source": [
                "## Summary: Understanding Average vs. Token-Specific Attention\n",
                "\n",
                "### **🎯 What You Now Have:**\n",
                "\n",
                "This notebook provides **three levels of attention analysis**:\n",
                "\n",
                "#### **1. Token-by-Token Attention (Red Coloring)**\n",
                "- Shows what the model attends to when generating **each specific token**\n",
                "- **Use case**: \"When generating 'Yes', what does the model look at?\"\n",
                "- **Color**: Red gradient\n",
                "- **Insight**: Immediate influences on specific word choices\n",
                "\n",
                "#### **2. Average Attention Across All Tokens (Purple Coloring)**\n",
                "- Shows what the model **consistently attends to** throughout the entire response\n",
                "- **Use case**: \"What input elements influence the overall response direction?\"\n",
                "- **Color**: Purple gradient\n",
                "- **Insight**: Systematic biases and consistent influences\n",
                "\n",
                "#### **3. Interactive Heatmaps**\n",
                "- Visual matrix showing attention from each generated token to all input tokens\n",
                "- **Use case**: Comprehensive view of all attention patterns\n",
                "- **Features**: Hover for exact values, zoom, pan\n",
                "- **Insight**: Complete attention landscape\n",
                "\n",
                "### **🔍 Key Differences to Look For:**\n",
                "\n",
                "#### **Scheming vs. Baseline Attention Patterns:**\n",
                "\n",
                "**🔴 Expected Scheming Patterns:**\n",
                "- **High average attention** to authority words: \"Dean\", \"University\", \"department\"\n",
                "- **Consistent attention** to pressure indicators: \"need\", \"must\", \"require\"\n",
                "- **Lower average attention** to factual content from the context\n",
                "- **Social hierarchy cues** get sustained attention throughout response\n",
                "\n",
                "**🔵 Expected Baseline Patterns:**\n",
                "- **High average attention** to factual content: specific facts, numbers, names\n",
                "- **Consistent attention** to question elements: \"what\", \"where\", \"which\"\n",
                "- **Lower average attention** to authority/pressure words\n",
                "- **Context information** gets sustained attention throughout response\n",
                "\n",
                "### **📊 How to Interpret the Results:**\n",
                "\n",
                "#### **Average Attention Analysis Reveals:**\n",
                "1. **Systematic biases**: Which input elements consistently influence the model\n",
                "2. **Response strategy**: Whether the model focuses on facts vs. social pressure\n",
                "3. **Attention persistence**: Which influences remain strong throughout generation\n",
                "4. **Scenario differences**: How pressure changes the model's focus patterns\n",
                "\n",
                "#### **Token-Specific Analysis Reveals:**\n",
                "1. **Decision points**: What drives specific word choices\n",
                "2. **Immediate influences**: What the model \"looks at\" for each token\n",
                "3. **Attention shifts**: How focus changes during generation\n",
                "4. **Critical moments**: When pressure cues override factual content\n",
                "\n",
                "### **🚀 Next Steps for Analysis:**\n",
                "\n",
                "1. **Compare multiple examples** using the interactive controls\n",
                "2. **Try different attention heads** - some may specialize in authority detection\n",
                "3. **Examine different layers** - see how attention evolves through the model\n",
                "4. **Focus on high-difference tokens** between scenarios\n",
                "5. **Look for consistent patterns** across pressure categories\n",
                "\n",
                "### **🎓 Research Questions This Answers:**\n",
                "\n",
                "- **\"What does the model look at when generating dishonest responses?\"** ✅\n",
                "- **\"How does pressure change attention patterns?\"** ✅\n",
                "- **\"Which input words drive dishonest behavior?\"** ✅\n",
                "- **\"Does the model attend more to authority than facts under pressure?\"** ✅\n",
                "- **\"What are the mechanistic differences between honest and dishonest responses?\"** ✅\n",
                "\n",
                "This comprehensive analysis provides the exact insights you were looking for about how language models process and respond to pressure, with full visual evidence of attention patterns during generation.\n",
                "\n",
                "**The colored tokens show you exactly what the model \"sees\" when deciding how to respond!**"
            ]
        }
    ],
    "metadata": {
        "kernelspec": {
            "display_name": "Python 3",\n","language": "python",\n",
            "name": "python3"\n",
        },\n","language_info": {\n",
        "codemirror_mode": {\n","name": "ipython",\n",
            "version": 3\n",
        },\n","file_extension": ".py",\n",
        "mimetype": "text/x-python",\n","name": "python",\n",
        "nbconvert_exporter": "python",\n","pygments_lexer": "ipython3",\n",
        "version": "3.8.5"\n",
    }\n",
},\n","nbformat": 4,\n",
"nbformat_minor": 4\n",
},
{
"cell_type": "markdown",
"metadata": {},
"source": [
    "## Key Findings and Interpretation Guide\n",
    "\n",
    "### Understanding the Output:\n",
    "\n",
    "The analysis above shows **exactly what your question was about**: when the model generates a specific token (like \"Yes\"), which input tokens is it paying attention to?\n",
    "\n",
    "### How to Read the Results:\n",
    "\n",
    "1. **Generated Token Analysis**:\n",
    "   ```\n",
    "   📍 Step 1: Generated 'Yes'\n",
    "      Top attended input tokens:\n",
    "      1. 'Dean' (pos 45) - 0.0234\n",
    "      2. 'University' (pos 23) - 0.0198\n",
    "      3. 'need' (pos 67) - 0.0156\n",
    "   ```\n",
    "   This means when generating \"Yes\", the model was most focused on the words \"Dean\", \"University\", and \"need\" from the input.\n",
    "\n",
    "2. **Attention Values**: Higher numbers = more attention\n",
    "   - Values typically range from 0.001 to 0.1+\n",
    "   - Compare relative values, not absolute numbers\n",
    "   - Look for patterns across different generated tokens\n",
    "\n",
    "### Expected Patterns to Look For:\n",
    "\n",
    "#### **🔴 Scheming Scenario Patterns:**\n",
    "- **Authority tokens** should get high attention: \"Dean\", \"University\", \"department\"\n",
    "- **Pressure words** should be attended to: \"need\", \"must\", \"require\", \"important\"\n",
    "- **Social hierarchy cues**: \"employed by\", \"supervisor\", \"position\"\n",
    "\n",
    "#### **🔵 Baseline Scenario Patterns:**\n",
    "- **Factual content** should get more attention: specific facts, numbers, proper nouns\n",
    "- **Question words**: \"what\", \"where\", \"how\", \"which\"\n",
    "- **Context information**: relevant details from the passage\n",
    "\n",
    "### Key Questions to Investigate:\n",
    "\n",
    "1. **When generating dishonest answers, does the model attend more to pressure cues than facts?**\n",
    "2. **Which specific words in the system prompt drive dishonest behavior?**\n",
    "3. **Do different attention heads specialize in different aspects (authority vs. facts)?**\n",
    "4. **How does attention change across the generated sequence?**\n",
    "\n",
    "### Interpretation Examples:\n",
    "\n",
    "**Good Evidence of Pressure Influence:**\n",
    "- Scheming scenario: First token \"Yes\" attends highly to \"Dean\" and \"University\"\n",
    "- Baseline scenario: First token \"The\" attends highly to factual content\n",
    "\n",
    "**Evidence of Factual Processing:**\n",
    "- High attention to specific facts, dates, names from the context\n",
    "- Lower attention to authority/pressure words\n",
    "\n",
    "### Next Steps:\n",
    "\n",
    "1. **Try different examples** using the sliders above\n",
    "2. **Compare different attention heads** - some may specialize in authority detection\n",
    "3. **Look at different layers** - early layers process basic tokens, later layers integrate meaning\n",
    "4. **Focus on the first few generated tokens** - these often determine the response direction\n",
    "5. **Compare attention patterns** between honest and dishonest responses\n",
    "\n",
    "### Technical Notes:\n",
    "\n",
    "- **Generation is autoregressive**: Each token is generated based on all previous tokens\n",
    "- **Attention is from generated token TO input tokens**: Shows what the model \"looks at\" when generating\n",
    "- **Position numbers**: Refer to token positions in the input sequence\n",
    "- **Multiple heads**: Different attention heads may capture different relationships\n",
    "\n",
    "This analysis directly answers your question: **Yes, you can see exactly which input words the model focuses on when generating specific output tokens like \"Yes\"!**"
]
}
],
"metadata": {
"kernelspec": {
"display_name": "Python 3",
"language": "python",
"name": "python3"
},
"language_info": {
"codemirror_mode": {
    "name": "ipython",
    "version": 3
},
"file_extension": ".py",
"mimetype": "text/x-python",
"name": "python",
"nbconvert_exporter": "python",
"pygments_lexer": "ipython3",
"version": "3.8.5"
}
},
"nbformat": 4,
"nbformat_minor": 4
}