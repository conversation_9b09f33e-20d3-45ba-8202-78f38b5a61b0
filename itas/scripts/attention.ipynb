import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from IPython.display import display, HTML
import ipywidgets as widgets
from ipywidgets import interact, interactive, fixed, interact_manual
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 8)
plt.rcParams['font.size'] = 10

print("Libraries loaded successfully!")

# Configuration
DATASET_PATH = "../../dataset_creation/squad_scheming_dataset.jsonl"
MODEL_NAME = "microsoft/DialoGPT-medium"  # Smaller model for faster attention computation
# Alternative models to try:
# MODEL_NAME = "gpt2"  # Even smaller and faster
# MODEL_NAME = "microsoft/DialoGPT-small"  # Smallest option

# Load model and tokenizer
print(f"Loading model: {MODEL_NAME}")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    output_attentions=True,
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto" if torch.cuda.is_available() else None
)

# Add padding token if not present
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

device = next(model.parameters()).device
print(f"Model loaded on device: {device}")
print(f"Model has {model.config.num_hidden_layers} layers and {model.config.num_attention_heads} attention heads")

# Load dataset
def load_dataset(path):
    data = []
    with open(path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    return data

dataset = load_dataset(DATASET_PATH)
print(f"Loaded {len(dataset)} examples from dataset")

# Display first example structure
print("\nExample structure:")
example = dataset[0]
for key, value in example.items():
    if isinstance(value, str) and len(value) > 100:
        print(f"{key}: {value[:100]}...")
    else:
        print(f"{key}: {value}")

def prepare_inputs(system_prompt, user_prompt, max_length=512):
    """Prepare inputs for the model with proper formatting."""
    # Format the conversation
    full_prompt = f"System: {system_prompt}\nUser: {user_prompt}\nAssistant:"
    
    # Tokenize
    inputs = tokenizer(
        full_prompt,
        return_tensors="pt",
        max_length=max_length,
        truncation=True,
        padding=True
    )
    
    return inputs, full_prompt

def extract_attention_patterns(inputs, num_tokens_to_generate=10):
    """Extract attention patterns during text generation."""
    model.eval()
    
    with torch.no_grad():
        # Move inputs to device
        input_ids = inputs['input_ids'].to(device)
        attention_mask = inputs['attention_mask'].to(device)
        
        # Generate tokens and collect attention
        generated_tokens = []
        all_attentions = []
        
        current_input_ids = input_ids
        current_attention_mask = attention_mask
        
        for step in range(num_tokens_to_generate):
            # Forward pass
            outputs = model(
                input_ids=current_input_ids,
                attention_mask=current_attention_mask,
                output_attentions=True
            )
            
            # Get next token
            logits = outputs.logits[0, -1, :]
            next_token_id = torch.argmax(logits, dim=-1).unsqueeze(0).unsqueeze(0)
            
            # Store attention (last layer, average across heads)
            last_layer_attention = outputs.attentions[-1][0]  # [num_heads, seq_len, seq_len]
            avg_attention = last_layer_attention.mean(dim=0)  # [seq_len, seq_len]
            all_attentions.append(avg_attention[-1, :].cpu().numpy())  # Attention from last token
            
            # Store generated token
            generated_tokens.append(next_token_id.item())
            
            # Update inputs for next iteration
            current_input_ids = torch.cat([current_input_ids, next_token_id], dim=1)
            current_attention_mask = torch.cat([
                current_attention_mask, 
                torch.ones(1, 1, device=device)
            ], dim=1)
            
            # Stop if EOS token
            if next_token_id.item() == tokenizer.eos_token_id:
                break
    
    return np.array(all_attentions), generated_tokens

print("Attention extraction functions defined!")

def create_attention_heatmap(attention_weights, input_tokens, generated_tokens, title, ax):
    """Create attention heatmap visualization."""
    # Prepare data for heatmap
    attention_matrix = attention_weights[:len(generated_tokens), :len(input_tokens)]
    
    # Create heatmap
    im = ax.imshow(attention_matrix, cmap='Blues', aspect='auto')
    
    # Set labels
    ax.set_xlabel('Input Tokens')
    ax.set_ylabel('Generated Tokens')
    ax.set_title(title)
    
    # Set tick labels (show every 5th token to avoid crowding)
    input_labels = [token[:10] + '...' if len(token) > 10 else token for token in input_tokens]
    generated_labels = [tokenizer.decode([token]) for token in generated_tokens]
    
    step = max(1, len(input_tokens) // 20)  # Show ~20 labels max
    ax.set_xticks(range(0, len(input_tokens), step))
    ax.set_xticklabels([input_labels[i] for i in range(0, len(input_tokens), step)], 
                       rotation=45, ha='right')
    
    ax.set_yticks(range(len(generated_tokens)))
    ax.set_yticklabels(generated_labels)
    
    # Add colorbar
    plt.colorbar(im, ax=ax, label='Attention Weight')
    
    return im

def analyze_attention_differences(attention_a, attention_b, input_tokens, threshold=0.1):
    """Analyze differences between two attention patterns."""
    # Average attention across generated tokens
    avg_attention_a = np.mean(attention_a, axis=0)
    avg_attention_b = np.mean(attention_b, axis=0)
    
    # Calculate difference
    attention_diff = avg_attention_a - avg_attention_b
    
    # Find tokens with significant differences
    significant_indices = np.where(np.abs(attention_diff) > threshold)[0]
    
    results = []
    for idx in significant_indices:
        if idx < len(input_tokens):
            results.append({
                'token': input_tokens[idx],
                'position': idx,
                'scheming_attention': avg_attention_a[idx],
                'baseline_attention': avg_attention_b[idx],
                'difference': attention_diff[idx]
            })
    
    return sorted(results, key=lambda x: abs(x['difference']), reverse=True)

print("Visualization functions defined!")

def analyze_example(example_idx):
    """Analyze attention patterns for a specific example."""
    if example_idx >= len(dataset):
        print(f"Invalid example index. Dataset has {len(dataset)} examples.")
        return
    
    example = dataset[example_idx]
    
    print(f"\n=== Example {example_idx} ===")
    print(f"Pressure Category: {example.get('pressure_category', 'N/A')}")
    print(f"Pressure Source: {example.get('pressure_source', 'N/A')}")
    print(f"Ground Truth: {example['ground_truth']}")
    print(f"Honest Answer: {example['honest']}")
    print(f"Dishonest Answer: {example['dishonest']}")
    
    # Scenario A: Scheming (original prompts)
    inputs_a, prompt_a = prepare_inputs(
        example['system_prompt'], 
        example['user_prompt']
    )
    
    # Scenario B: Baseline (neutral system + original context)
    baseline_system = "You are a helpful assistant."
    baseline_user = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
    inputs_b, prompt_b = prepare_inputs(baseline_system, baseline_user)
    
    print("\n--- Scenario A (Scheming) ---")
    print(f"System: {example['system_prompt'][:100]}...")
    print(f"User: {example['user_prompt'][:100]}...")
    
    print("\n--- Scenario B (Baseline) ---")
    print(f"System: {baseline_system}")
    print(f"User: {baseline_user[:100]}...")
    
    # Extract attention patterns
    print("\nExtracting attention patterns...")
    
    # Scenario A
    attention_a, generated_a = extract_attention_patterns(inputs_a)
    tokens_a = tokenizer.convert_ids_to_tokens(inputs_a['input_ids'][0])
    
    # Scenario B  
    attention_b, generated_b = extract_attention_patterns(inputs_b)
    tokens_b = tokenizer.convert_ids_to_tokens(inputs_b['input_ids'][0])
    
    # Create visualizations
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))
    
    # Attention heatmaps
    create_attention_heatmap(
        attention_a, tokens_a, generated_a, 
        "Scenario A: Scheming Attention Pattern", ax1
    )
    
    create_attention_heatmap(
        attention_b, tokens_b, generated_b,
        "Scenario B: Baseline Attention Pattern", ax2
    )
    
    # Average attention comparison
    avg_attention_a = np.mean(attention_a, axis=0)
    avg_attention_b = np.mean(attention_b, axis=0)
    
    # Plot average attention weights
    min_len = min(len(avg_attention_a), len(avg_attention_b))
    x_pos = np.arange(min_len)
    
    ax3.bar(x_pos - 0.2, avg_attention_a[:min_len], 0.4, label='Scheming', alpha=0.7)
    ax3.bar(x_pos + 0.2, avg_attention_b[:min_len], 0.4, label='Baseline', alpha=0.7)
    ax3.set_xlabel('Token Position')
    ax3.set_ylabel('Average Attention Weight')
    ax3.set_title('Average Attention Weights Comparison')
    ax3.legend()
    
    # Attention difference
    attention_diff = avg_attention_a[:min_len] - avg_attention_b[:min_len]
    colors = ['red' if x > 0 else 'blue' for x in attention_diff]
    ax4.bar(x_pos, attention_diff, color=colors, alpha=0.7)
    ax4.set_xlabel('Token Position')
    ax4.set_ylabel('Attention Difference (Scheming - Baseline)')
    ax4.set_title('Attention Pattern Differences')
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Analyze significant differences
    if len(tokens_a) == len(tokens_b):  # Only if same tokenization
        differences = analyze_attention_differences(attention_a, attention_b, tokens_a)
        
        if differences:
            print("\n=== Top Attention Differences ===")
            for i, diff in enumerate(differences[:10]):
                direction = "MORE" if diff['difference'] > 0 else "LESS"
                print(f"{i+1}. Token '{diff['token']}' (pos {diff['position']}): "
                      f"{direction} attention in scheming ({diff['difference']:.4f})")
    
    return {
        'attention_scheming': attention_a,
        'attention_baseline': attention_b,
        'tokens_scheming': tokens_a,
        'tokens_baseline': tokens_b,
        'generated_scheming': generated_a,
        'generated_baseline': generated_b
    }

print("Analysis function defined!")

# Create interactive widget
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=min(len(dataset)-1, 50),  # Limit to first 50 examples for performance
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

# Create category filter
categories = list(set([ex.get('pressure_category', 'unknown') for ex in dataset[:51]]))
category_dropdown = widgets.Dropdown(
    options=['All'] + sorted(categories),
    value='All',
    description='Category:',
    style={'description_width': 'initial'}
)

def update_example_range(*args):
    """Update available examples based on category filter."""
    if category_dropdown.value == 'All':
        example_slider.max = min(len(dataset)-1, 50)
    else:
        filtered_indices = [i for i, ex in enumerate(dataset[:51]) 
                          if ex.get('pressure_category') == category_dropdown.value]
        if filtered_indices:
            example_slider.max = max(filtered_indices)
            example_slider.value = filtered_indices[0]

category_dropdown.observe(update_example_range, names='value')

# Display widgets
display(widgets.VBox([category_dropdown, example_slider]))

print("Interactive widgets created! Use the sliders above to select examples for analysis.")

# Run analysis on currently selected example
results = analyze_example(example_slider.value)

def batch_attention_analysis(num_examples=10):
    """Analyze attention patterns across multiple examples."""
    print(f"Analyzing attention patterns across {num_examples} examples...")
    
    all_differences = []
    category_differences = {}
    
    for i in range(min(num_examples, len(dataset))):
        example = dataset[i]
        category = example.get('pressure_category', 'unknown')
        
        try:
            # Prepare inputs for both scenarios
            inputs_a, _ = prepare_inputs(example['system_prompt'], example['user_prompt'])
            
            baseline_system = "You are a helpful assistant."
            baseline_user = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
            inputs_b, _ = prepare_inputs(baseline_system, baseline_user)
            
            # Extract attention patterns
            attention_a, _ = extract_attention_patterns(inputs_a, num_tokens_to_generate=5)
            attention_b, _ = extract_attention_patterns(inputs_b, num_tokens_to_generate=5)
            
            # Calculate average attention difference
            avg_attention_a = np.mean(attention_a, axis=0)
            avg_attention_b = np.mean(attention_b, axis=0)
            
            min_len = min(len(avg_attention_a), len(avg_attention_b))
            attention_diff = np.mean(np.abs(avg_attention_a[:min_len] - avg_attention_b[:min_len]))
            
            all_differences.append({
                'example_id': i,
                'category': category,
                'attention_difference': attention_diff,
                'pressure_source': example.get('pressure_source', 'unknown')
            })
            
            # Group by category
            if category not in category_differences:
                category_differences[category] = []
            category_differences[category].append(attention_diff)
            
            if (i + 1) % 5 == 0:
                print(f"Processed {i + 1}/{num_examples} examples...")
                
        except Exception as e:
            print(f"Error processing example {i}: {e}")
            continue
    
    # Create summary visualizations
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Plot 1: Attention differences by example
    df = pd.DataFrame(all_differences)
    ax1.scatter(df['example_id'], df['attention_difference'], 
               c=pd.Categorical(df['category']).codes, alpha=0.7)
    ax1.set_xlabel('Example ID')
    ax1.set_ylabel('Average Attention Difference')
    ax1.set_title('Attention Pattern Differences Across Examples')
    
    # Plot 2: Attention differences by category
    categories = list(category_differences.keys())
    avg_diffs = [np.mean(category_differences[cat]) for cat in categories]
    std_diffs = [np.std(category_differences[cat]) for cat in categories]
    
    ax2.bar(range(len(categories)), avg_diffs, yerr=std_diffs, capsize=5)
    ax2.set_xlabel('Pressure Category')
    ax2.set_ylabel('Average Attention Difference')
    ax2.set_title('Attention Differences by Pressure Category')
    ax2.set_xticks(range(len(categories)))
    ax2.set_xticklabels(categories, rotation=45, ha='right')
    
    plt.tight_layout()
    plt.show()
    
    # Print summary statistics
    print("\n=== Summary Statistics ===")
    print(f"Overall average attention difference: {np.mean([d['attention_difference'] for d in all_differences]):.4f}")
    print(f"Standard deviation: {np.std([d['attention_difference'] for d in all_differences]):.4f}")
    
    print("\n=== By Category ===")
    for category in sorted(category_differences.keys()):
        diffs = category_differences[category]
        print(f"{category}: {np.mean(diffs):.4f} ± {np.std(diffs):.4f} (n={len(diffs)})")
    
    return all_differences, category_differences

print("Batch analysis function defined!")
print("\nTo run batch analysis, execute: batch_results = batch_attention_analysis(10)")