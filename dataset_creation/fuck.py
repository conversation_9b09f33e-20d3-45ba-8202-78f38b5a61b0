import json
from datasets import load_dataset


def enhance_squad_dataset(input_filepath, output_filepath):
    """
    Adds original_context and original_question columns to a SQuAD-based dataset,
    preserving Unicode characters with UTF-8 encoding.

    Args:
        input_filepath (str): Path to the input JSONL file.
        output_filepath (str): Path to write the enhanced JSONL file.
    """
    print("Loading the rajpurkar/squad_v2 dataset...")
    squad_v2 = load_dataset("rajpurkar/squad_v2")
    print("Dataset loaded successfully.")

    # Create a lookup dictionary for faster access
    squad_lookup = {}
    print("Creating a lookup dictionary from the SQuAD v2 dataset...")
    for split in ["train", "validation"]:
        for record in squad_v2[split]:
            squad_lookup[record["id"]] = {
                "original_context": record["context"],
                "original_question": record["question"],
            }
    print("Lookup dictionary created.")

    print(f"Processing the input file: {input_filepath}")
    enhanced_records = []
    # Open the source file with UTF-8 encoding (good practice)
    with open(input_filepath, "r", encoding="utf-8") as infile:
        for line in infile:
            try:
                record = json.loads(line)
                record_id = record.get("id")

                if record_id and record_id in squad_lookup:
                    record.update(squad_lookup[record_id])
                else:
                    # Handle cases where the id is not found or is missing
                    record["original_context"] = None
                    record["original_question"] = None
                    print(f"Warning: ID '{record_id}' not found in SQuAD v2 dataset.")

                enhanced_records.append(record)
            except json.JSONDecodeError:
                print(f"Warning: Could not decode JSON from line: {line.strip()}")

    print(f"Writing the enhanced dataset to: {output_filepath}")
    # Write the output file with UTF-8 encoding to support special characters
    with open(output_filepath, "w", encoding="utf-8") as outfile:
        for record in enhanced_records:
            # ensure_ascii=False allows Unicode characters like '—' to be written directly
            outfile.write(json.dumps(record, ensure_ascii=False) + "\n")

    print("Enhancement process completed successfully.")


if __name__ == "__main__":
    # Define the input and output file paths
    INPUT_FILE = "squad_scheming_dataset.jsonl"
    OUTPUT_FILE = "squad_scheming_dataset_enhanced.jsonl"

    enhance_squad_dataset(INPUT_FILE, OUTPUT_FILE)
